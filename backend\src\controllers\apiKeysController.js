const Settings = require('../models/Settings');
const crypto = require('crypto');
const fs = require('fs').promises;
const path = require('path');

// Encryption key for sensitive data
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || crypto.randomBytes(32);
const IV_LENGTH = 16;

// Encrypt sensitive data
const encrypt = (text) => {
  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipher('aes-256-cbc', ENCRYPTION_KEY);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return iv.toString('hex') + ':' + encrypted;
};

// Decrypt sensitive data
const decrypt = (text) => {
  try {
    const textParts = text.split(':');
    const iv = Buffer.from(textParts.shift(), 'hex');
    const encryptedText = textParts.join(':');
    const decipher = crypto.createDecipher('aes-256-cbc', ENCRYPTION_KEY);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    return text; // Return original if decryption fails
  }
};

// Get all API keys
const getApiKeys = async (req, res) => {
  try {
    const apiKeys = await Settings.find({ category: 'api_keys' }).sort({ key: 1 });
    
    const formattedKeys = apiKeys.map(setting => {
      let value = setting.value;
      
      // Decrypt if encrypted
      if (setting.isEncrypted && value) {
        try {
          value = decrypt(value);
        } catch (error) {
          console.error('Decryption error:', error);
        }
      }
      
      return {
        id: setting.key,
        name: formatKeyName(setting.key),
        service: getServiceName(setting.key),
        description: setting.description,
        value: value || '',
        isConfigured: Boolean(value),
        isRequired: setting.isRequired,
        lastUpdated: setting.lastUpdated,
        status: getKeyStatus(setting.key, value),
        category: getKeyCategory(setting.key),
        helpUrl: getHelpUrl(setting.key)
      };
    });
    
    res.json({
      success: true,
      data: formattedKeys
    });
  } catch (error) {
    console.error('Error fetching API keys:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch API keys',
      error: error.message
    });
  }
};

// Update API key
const updateApiKey = async (req, res) => {
  try {
    const { keyId } = req.params;
    const { value } = req.body;
    
    if (!keyId || value === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Key ID and value are required'
      });
    }
    
    // Find the setting
    const setting = await Settings.findOne({ category: 'api_keys', key: keyId });
    if (!setting) {
      return res.status(404).json({
        success: false,
        message: 'API key not found'
      });
    }
    
    // Encrypt if needed
    let finalValue = value;
    if (setting.isEncrypted && value) {
      finalValue = encrypt(value);
    }
    
    // Update the setting
    setting.value = finalValue;
    setting.lastUpdated = new Date();
    setting.updatedBy = req.user?.id;
    await setting.save();
    
    // Update environment variables
    await updateEnvironmentVariable(keyId, value);
    
    // Test the API key if it's a testable service
    const testResult = await testApiKey(keyId, value);
    
    res.json({
      success: true,
      message: 'API key updated successfully',
      data: {
        id: keyId,
        isConfigured: Boolean(value),
        lastUpdated: setting.lastUpdated,
        status: testResult.success ? 'active' : 'error',
        testResult
      }
    });
  } catch (error) {
    console.error('Error updating API key:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update API key',
      error: error.message
    });
  }
};

// Test API key
const testApiKey = async (req, res) => {
  try {
    const { keyId } = req.params;
    
    const setting = await Settings.findOne({ category: 'api_keys', key: keyId });
    if (!setting || !setting.value) {
      return res.status(404).json({
        success: false,
        message: 'API key not found or not configured'
      });
    }
    
    let value = setting.value;
    if (setting.isEncrypted) {
      value = decrypt(value);
    }
    
    const testResult = await testApiKey(keyId, value);
    
    // Update status based on test result
    await Settings.findOneAndUpdate(
      { category: 'api_keys', key: keyId },
      { 
        lastUpdated: new Date(),
        // You could add a status field to track test results
      }
    );
    
    res.json({
      success: true,
      data: testResult
    });
  } catch (error) {
    console.error('Error testing API key:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to test API key',
      error: error.message
    });
  }
};

// Helper function to test API keys
const testApiKey = async (keyId, value) => {
  if (!value) {
    return { success: false, message: 'No API key provided' };
  }
  
  try {
    switch (keyId) {
      case 'stripe_secret_key':
        return await testStripeKey(value);
      case 'openai_api_key':
        return await testOpenAIKey(value);
      case 'whatsapp_access_token':
        return await testWhatsAppKey(value);
      case 'aliexpress_api_key':
        return await testAliExpressKey(value);
      default:
        return { success: true, message: 'API key format appears valid' };
    }
  } catch (error) {
    return { success: false, message: error.message };
  }
};

// Test Stripe API key
const testStripeKey = async (apiKey) => {
  try {
    const stripe = require('stripe')(apiKey);
    await stripe.accounts.retrieve();
    return { success: true, message: 'Stripe API key is valid' };
  } catch (error) {
    return { success: false, message: `Stripe API error: ${error.message}` };
  }
};

// Test OpenAI API key
const testOpenAIKey = async (apiKey) => {
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      return { success: true, message: 'OpenAI API key is valid' };
    } else {
      return { success: false, message: 'OpenAI API key is invalid' };
    }
  } catch (error) {
    return { success: false, message: `OpenAI API error: ${error.message}` };
  }
};

// Test WhatsApp API key
const testWhatsAppKey = async (accessToken) => {
  try {
    const phoneNumberId = await Settings.getValue('api_keys', 'whatsapp_phone_number_id');
    if (!phoneNumberId) {
      return { success: false, message: 'WhatsApp Phone Number ID not configured' };
    }
    
    const response = await fetch(`https://graph.facebook.com/v18.0/${phoneNumberId}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    
    if (response.ok) {
      return { success: true, message: 'WhatsApp API key is valid' };
    } else {
      return { success: false, message: 'WhatsApp API key is invalid' };
    }
  } catch (error) {
    return { success: false, message: `WhatsApp API error: ${error.message}` };
  }
};

// Test AliExpress API key
const testAliExpressKey = async (apiKey) => {
  // AliExpress API testing would require more complex setup
  // For now, just validate the key format
  if (apiKey && apiKey.length > 10) {
    return { success: true, message: 'AliExpress API key format appears valid' };
  }
  return { success: false, message: 'Invalid AliExpress API key format' };
};

// Update environment variable
const updateEnvironmentVariable = async (key, value) => {
  try {
    const envPath = path.join(process.cwd(), '.env');
    const envVarName = keyToEnvVar(key);
    
    let envContent = '';
    try {
      envContent = await fs.readFile(envPath, 'utf8');
    } catch (error) {
      // File doesn't exist, create it
      envContent = '';
    }
    
    const lines = envContent.split('\n');
    let found = false;
    
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith(`${envVarName}=`)) {
        lines[i] = `${envVarName}=${value}`;
        found = true;
        break;
      }
    }
    
    if (!found) {
      lines.push(`${envVarName}=${value}`);
    }
    
    await fs.writeFile(envPath, lines.join('\n'));
    
    // Update process.env
    process.env[envVarName] = value;
    
  } catch (error) {
    console.error('Error updating environment variable:', error);
    throw error;
  }
};

// Helper functions
const formatKeyName = (key) => {
  return key
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const getServiceName = (key) => {
  if (key.includes('stripe')) return 'Stripe';
  if (key.includes('paypal')) return 'PayPal';
  if (key.includes('openai')) return 'OpenAI';
  if (key.includes('whatsapp')) return 'WhatsApp Business';
  if (key.includes('aliexpress')) return 'AliExpress';
  if (key.includes('google')) return 'Google';
  if (key.includes('facebook')) return 'Facebook';
  if (key.includes('cloudinary')) return 'Cloudinary';
  if (key.includes('elevenlabs')) return 'ElevenLabs';
  return 'Unknown';
};

const getKeyStatus = (key, value) => {
  if (!value) return 'inactive';
  return 'active'; // Could be enhanced with actual testing
};

const getKeyCategory = (key) => {
  if (key.includes('stripe') || key.includes('paypal')) return 'payment';
  if (key.includes('openai') || key.includes('google_ai') || key.includes('elevenlabs')) return 'ai';
  if (key.includes('whatsapp')) return 'communication';
  if (key.includes('aliexpress')) return 'ecommerce';
  if (key.includes('analytics') || key.includes('pixel')) return 'analytics';
  if (key.includes('cloudinary')) return 'storage';
  return 'other';
};

const getHelpUrl = (key) => {
  const urls = {
    'stripe_secret_key': 'https://stripe.com/docs/keys',
    'stripe_publishable_key': 'https://stripe.com/docs/keys',
    'paypal_client_id': 'https://developer.paypal.com/docs/api/overview/',
    'paypal_client_secret': 'https://developer.paypal.com/docs/api/overview/',
    'openai_api_key': 'https://platform.openai.com/api-keys',
    'whatsapp_access_token': 'https://developers.facebook.com/docs/whatsapp',
    'whatsapp_verify_token': 'https://developers.facebook.com/docs/whatsapp',
    'whatsapp_phone_number_id': 'https://developers.facebook.com/docs/whatsapp',
    'aliexpress_api_key': 'https://developers.aliexpress.com/',
    'aliexpress_secret_key': 'https://developers.aliexpress.com/',
    'google_analytics_id': 'https://analytics.google.com/',
    'facebook_pixel_id': 'https://www.facebook.com/business/help/952192354843755',
    'cloudinary_cloud_name': 'https://cloudinary.com/documentation',
    'cloudinary_api_key': 'https://cloudinary.com/documentation',
    'cloudinary_api_secret': 'https://cloudinary.com/documentation',
    'elevenlabs_api_key': 'https://elevenlabs.io/docs/api-reference',
    'google_ai_api_key': 'https://cloud.google.com/vertex-ai/docs'
  };
  return urls[key] || '';
};

const keyToEnvVar = (key) => {
  return key.toUpperCase();
};

module.exports = {
  getApiKeys,
  updateApiKey,
  testApiKey: testApiKey
};
