'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { wishlistService } from '@/services/wishlistService';

interface Product {
  _id: string;
  name: string;
  price: number;
  images: string[];
  slug: string;
  rating: number;
}

interface WishlistContextType {
  wishlist: Product[];
  isLoading: boolean;
  error: string | null;
  addToWishlist: (productId: string) => Promise<void>;
  removeFromWishlist: (productId: string) => Promise<void>;
  isInWishlist: (productId: string) => boolean;
  clearWishlist: () => void;
  refreshWishlist: () => Promise<void>;
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

export const useWishlist = () => {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};

interface WishlistProviderProps {
  children: ReactNode;
}

export const WishlistProvider: React.FC<WishlistProviderProps> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const [wishlist, setWishlist] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load wishlist when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      refreshWishlist();
    } else {
      setWishlist([]);
    }
  }, [isAuthenticated, user]);

  const refreshWishlist = async () => {
    if (!isAuthenticated) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await wishlistService.getWishlist();
      setWishlist(data.wishlist || []);
    } catch (err: any) {
      setError(err.message || 'Failed to load wishlist');
      console.error('Error loading wishlist:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const addToWishlist = async (productId: string) => {
    if (!isAuthenticated) {
      setError('Please log in to add items to your wishlist');
      return;
    }

    setError(null);
    
    try {
      const data = await wishlistService.addToWishlist(productId);
      setWishlist(data.wishlist || []);
    } catch (err: any) {
      setError(err.message || 'Failed to add item to wishlist');
      throw err;
    }
  };

  const removeFromWishlist = async (productId: string) => {
    if (!isAuthenticated) return;

    setError(null);
    
    try {
      const data = await wishlistService.removeFromWishlist(productId);
      setWishlist(data.wishlist || []);
    } catch (err: any) {
      setError(err.message || 'Failed to remove item from wishlist');
      throw err;
    }
  };

  const isInWishlist = (productId: string): boolean => {
    return wishlist.some(item => item._id === productId);
  };

  const clearWishlist = () => {
    setWishlist([]);
    setError(null);
  };

  const value: WishlistContextType = {
    wishlist,
    isLoading,
    error,
    addToWishlist,
    removeFromWishlist,
    isInWishlist,
    clearWishlist,
    refreshWishlist,
  };

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  );
};
