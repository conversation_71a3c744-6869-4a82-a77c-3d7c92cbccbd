'use client';

import React from 'react';
import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { CartProvider } from '@/contexts/CartContext';
import { CheckoutProvider } from '@/contexts/CheckoutContext';
import { AuthProvider } from '@/contexts/AuthContext';
import { SettingsProvider } from '@/contexts/SettingsContext';
import { WishlistProvider } from '@/contexts/WishlistContext';
import { ComparisonProvider } from '@/contexts/ComparisonContext';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <AuthProvider>
        <SettingsProvider>
          <ThemeProvider>
            <ComparisonProvider>
              <WishlistProvider>
                <CartProvider>
                  <CheckoutProvider>
                    {children}
                  </CheckoutProvider>
                </CartProvider>
              </WishlistProvider>
            </ComparisonProvider>
          </ThemeProvider>
        </SettingsProvider>
      </AuthProvider>
    </SessionProvider>
  );
}