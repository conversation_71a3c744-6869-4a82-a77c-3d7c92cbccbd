'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

const footerNavigation = {
  shop: [
    { name: 'All Wigs', href: '/shop' },
    { name: 'Custom Wigs', href: '/shop/custom' },
    { name: 'Lace Front Wigs', href: '/shop/lace-front' },
    { name: 'Full Lace Wigs', href: '/shop/full-lace' },
    { name: 'Accessories', href: '/shop/accessories' },
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Our Story', href: '/our-story' },
    { name: 'Testimonials', href: '/testimonials' },
    { name: 'Blog', href: '/blog' },
    { name: 'Careers', href: '/careers' },
  ],
  support: [
    { name: 'Contact', href: '/contact' },
    { name: 'FAQs', href: '/faq' },
    { name: 'Shipping', href: '/shipping' },
    { name: 'Returns', href: '/returns' },
    { name: 'Size Guide', href: '/size-guide' },
  ],
  legal: [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Accessibility', href: '/accessibility' },
  ],
  social: [
    {
      name: 'Instagram',
      href: '#',
      icon: (props: React.SVGProps<SVGSVGElement>) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path
            fillRule="evenodd"
            d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"
            clipRule="evenodd"
          />
        </svg>
      ),
    },
    {
      name: 'Facebook',
      href: '#',
      icon: (props: React.SVGProps<SVGSVGElement>) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path
            fillRule="evenodd"
            d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"
            clipRule="evenodd"
          />
        </svg>
      ),
    },
    {
      name: 'Twitter',
      href: '#',
      icon: (props: React.SVGProps<SVGSVGElement>) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
        </svg>
      ),
    },
    {
      name: 'YouTube',
      href: '#',
      icon: (props: React.SVGProps<SVGSVGElement>) => (
        <svg fill="currentColor" viewBox="0 0 24 24" {...props}>
          <path
            fillRule="evenodd"
            d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z"
            clipRule="evenodd"
          />
        </svg>
      ),
    },
  ],
};

const Footer = () => {
  const router = useRouter();
  const { isAuthenticated, user } = useAuth();
  const [showAdminModal, setShowAdminModal] = useState(false);
  
  const handleAdminClick = () => {
    if (isAuthenticated && (user?.role === 'admin' || user?.role === 'super-admin')) {
      router.push('/admin/dashboard');
    } else {
      setShowAdminModal(true);
    }
  };
  
  const closeModal = () => {
    setShowAdminModal(false);
  };
  
  const redirectToAdminLogin = () => {
    router.push('/auth/login?callbackUrl=/admin/dashboard');
    closeModal();
  };
  
  return (
    <footer className="bg-gray-900 text-white pt-12 pb-8">
      <div className="container mx-auto px-4">
        <div className="flex flex-wrap -mx-4">
          {/* Logo and about */}
          <div className="w-full md:w-4/12 px-4 mb-8 md:mb-0">
            <div className="flex items-center mb-4">
              <div className="relative h-12 w-12 mr-3 rounded-lg overflow-hidden">
                <Image src="/pelucaschiclogo.png" alt="Pelucas Chic" fill className="object-contain" />
              </div>
              <h3 className="text-xl font-bold">Pelucas Chic</h3>
            </div>
            <p className="text-gray-400 mb-4">
              Premium quality wigs for every style, occasion, and budget. Experience the confidence that comes with a perfect look.
            </p>
            <div className="flex mt-4">
              {footerNavigation.social.map((item) => (
                <a key={item.name} href={item.href} target="_blank" rel="noopener noreferrer" className="mr-3 text-gray-400 hover:text-white">
                  <item.icon className="h-6 w-6" aria-hidden="true" />
                </a>
              ))}
            </div>
          </div>
          
          {/* Quick links */}
          <div className="w-full md:w-2/12 px-4 mb-8 md:mb-0">
            <h5 className="text-xl font-semibold mb-5">Quick Links</h5>
            <ul className="list-none">
              <li className="mb-3">
                <Link href="/catalog" className="text-gray-400 hover:text-white">Shop</Link>
              </li>
              <li className="mb-3">
                <Link href="/blog" className="text-gray-400 hover:text-white">Blog</Link>
              </li>
              <li className="mb-3">
                <Link href="/about" className="text-gray-400 hover:text-white">About Us</Link>
              </li>
              <li className="mb-3">
                <Link href="/contact" className="text-gray-400 hover:text-white">Contact</Link>
              </li>
              <li className="mb-3">
                <Link href="/faq" className="text-gray-400 hover:text-white">FAQ</Link>
              </li>
            </ul>
          </div>
          
          {/* Categories */}
          <div className="w-full md:w-2/12 px-4 mb-8 md:mb-0">
            <h5 className="text-xl font-semibold mb-5">Categories</h5>
            <ul className="list-none">
              <li className="mb-3">
                <Link href="/catalog?category=lace-front" className="text-gray-400 hover:text-white">Lace Front</Link>
              </li>
              <li className="mb-3">
                <Link href="/catalog?category=full-lace" className="text-gray-400 hover:text-white">Full Lace</Link>
              </li>
              <li className="mb-3">
                <Link href="/catalog?category=360-lace" className="text-gray-400 hover:text-white">360° Lace</Link>
              </li>
              <li className="mb-3">
                <Link href="/catalog?category=synthetic" className="text-gray-400 hover:text-white">Synthetic</Link>
              </li>
              <li className="mb-3">
                <Link href="/catalog?category=human-hair" className="text-gray-400 hover:text-white">Human Hair</Link>
              </li>
            </ul>
          </div>
          
          {/* Customer service */}
          <div className="w-full md:w-2/12 px-4 mb-8 md:mb-0">
            <h5 className="text-xl font-semibold mb-5">Customer Service</h5>
            <ul className="list-none">
              <li className="mb-3">
                <Link href="/shipping" className="text-gray-400 hover:text-white">Shipping</Link>
              </li>
              <li className="mb-3">
                <Link href="/returns" className="text-gray-400 hover:text-white">Returns</Link>
              </li>
              <li className="mb-3">
                <Link href="/privacy" className="text-gray-400 hover:text-white">Privacy Policy</Link>
              </li>
              <li className="mb-3">
                <Link href="/terms" className="text-gray-400 hover:text-white">Terms of Service</Link>
              </li>
              <li className="mb-3">
                <Link href="/track-order" className="text-gray-400 hover:text-white">Track Order</Link>
              </li>
            </ul>
          </div>
          
          {/* Newsletter */}
          <div className="w-full md:w-2/12 px-4">
            <h5 className="text-xl font-semibold mb-5">Stay Updated</h5>
            <p className="text-gray-400 mb-4">Subscribe to our newsletter for the latest products, tips, and promotions.</p>
            <form className="mb-4">
              <div className="flex flex-wrap">
                <input
                  type="email"
                  placeholder="Your email"
                  className="w-full px-4 py-2 mb-2 text-gray-900 bg-white rounded focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <button
                  type="submit"
                  className="w-full px-4 py-2 font-bold text-white bg-primary rounded hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  Subscribe
                </button>
              </div>
            </form>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-wrap justify-between items-center">
            <div className="w-full md:w-auto mb-4 md:mb-0">
              <p className="text-sm text-gray-400">
                &copy; {new Date().getFullYear()} Pelucas Chic. All rights reserved.
              </p>
            </div>
            <div className="w-full md:w-auto">
              <div className="flex items-center justify-center md:justify-end">
                <img src="/images/payment/visa.svg" alt="Visa" className="h-6 mr-2" />
                <img src="/images/payment/mastercard.svg" alt="Mastercard" className="h-6 mr-2" />
                <img src="/images/payment/amex.svg" alt="American Express" className="h-6 mr-2" />
                <img src="/images/payment/paypal.svg" alt="PayPal" className="h-6" />
              </div>
            </div>
          </div>
        </div>
        
        {/* Admin Login Button - Small and subtle */}
        <div className="text-center mt-8">
          <button
            onClick={handleAdminClick}
            className="text-xs text-gray-500 hover:text-gray-400 focus:outline-none"
          >
            Admin
          </button>
        </div>
      </div>
      
      {/* Admin Login Modal */}
      {showAdminModal && (
        <div className="fixed z-50 inset-0 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75" onClick={closeModal}></div>
            </div>
            
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Admin Access</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        You need to be logged in as an admin to access the administration panel. Would you like to proceed to the admin login page?
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={redirectToAdminLogin}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Go to Login
                </button>
                <button
                  type="button"
                  onClick={closeModal}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </footer>
  );
};

export default Footer; 