{"name": "frontend", "version": "1.0.0", "description": "", "main": "next.config.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/bcrypt": "^5.0.2", "@types/crypto-js": "^4.2.2", "@types/node": "^20.11.25", "@types/pg": "^8.15.2", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@types/uuid": "^10.0.0", "axios": "^1.6.7", "bcrypt": "^6.0.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "currency.js": "^2.0.4", "date-fns": "^3.3.1", "dotenv": "^16.5.0", "framer-motion": "^12.18.1", "gsap": "^3.13.0", "i18next-browser-languagedetector": "^7.2.0", "intersection-observer": "^0.12.2", "locomotive-scroll": "^4.1.4", "lucide-react": "^0.511.0", "motion": "^12.12.2", "next": "^14.1.3", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "openai": "^5.6.0", "pg": "^8.16.0", "react": "^18.2.0", "react-currency-input-field": "^3.6.11", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-swipeable": "^7.0.2", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.3.0", "typescript": "^5.4.2", "uuid": "^11.1.0", "zod": "^3.25.23"}, "devDependencies": {"@babel/core": "^7.27.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.3"}}