'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/contexts/SubscriptionContext';
import { Subscription } from '@/services/subscriptionService';
import MainLayout from '@/components/layout/MainLayout';

interface SubscriptionCardProps {
  subscription: Subscription;
  onPause: () => void;
  onResume: () => void;
  onCancel: () => void;
  onSkip: () => void;
  onEdit: () => void;
}

const SubscriptionCard: React.FC<SubscriptionCardProps> = ({
  subscription,
  onPause,
  onResume,
  onCancel,
  onSkip,
  onEdit
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'expired': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <svg className="w-4 h-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'paused':
        return (
          <svg className="w-4 h-4 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'cancelled':
        return (
          <svg className="w-4 h-4 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
    >
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{subscription.plan.name}</h3>
            <p className="text-sm text-gray-600">{subscription.plan.description}</p>
          </div>
          <div className="flex items-center space-x-2">
            {getStatusIcon(subscription.status)}
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(subscription.status)}`}>
              {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
            </span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Products */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Products</h4>
            <div className="space-y-3">
              {subscription.products.map((product, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="relative w-12 h-12 flex-shrink-0">
                    <Image
                      src={product.image || '/images/placeholder.jpg'}
                      alt={product.productName}
                      fill
                      className="object-cover rounded-md"
                      sizes="48px"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {product.productName}
                    </p>
                    <p className="text-sm text-gray-500">
                      Qty: {product.quantity} • ${product.price.toFixed(2)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Details */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Subscription Details</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Next Delivery:</span>
                <span className="font-medium">{formatDate(subscription.nextDelivery)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Frequency:</span>
                <span className="font-medium">
                  {subscription.plan.interval === 'monthly' ? 'Monthly' :
                   subscription.plan.interval === 'quarterly' ? 'Every 3 months' :
                   subscription.plan.interval === 'biannual' ? 'Every 6 months' : 'Yearly'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Discount:</span>
                <span className="font-medium text-green-600">{subscription.plan.discount}% off</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total Amount:</span>
                <span className="font-medium">${subscription.totalAmount.toFixed(2)}</span>
              </div>
              {subscription.discountAmount > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">You Save:</span>
                  <span className="font-medium text-green-600">${subscription.discountAmount.toFixed(2)}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="flex flex-wrap gap-3">
            {subscription.status === 'active' && (
              <>
                <button
                  onClick={onPause}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
                >
                  Pause
                </button>
                <button
                  onClick={onSkip}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
                >
                  Skip Next
                </button>
              </>
            )}
            
            {subscription.status === 'paused' && (
              <button
                onClick={onResume}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors duration-200"
              >
                Resume
              </button>
            )}
            
            <button
              onClick={onEdit}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
            >
              Edit
            </button>
            
            {subscription.status !== 'cancelled' && (
              <button
                onClick={onCancel}
                className="px-4 py-2 text-red-600 hover:text-red-800 transition-colors duration-200"
              >
                Cancel
              </button>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default function SubscriptionsPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { 
    subscriptions, 
    isLoading, 
    error, 
    pauseSubscription, 
    resumeSubscription, 
    cancelSubscription, 
    skipNextDelivery 
  } = useSubscription();
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const router = useRouter();

  // Redirect to login if not authenticated
  React.useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login?callbackUrl=/account/subscriptions');
    }
  }, [isAuthenticated, authLoading, router]);

  const handleAction = async (action: () => Promise<void>, subscriptionId: string) => {
    setActionLoading(subscriptionId);
    try {
      await action();
    } catch (error) {
      console.error('Action failed:', error);
      // Could show a toast notification here
    } finally {
      setActionLoading(null);
    }
  };

  // Show loading state
  if (authLoading || isLoading) {
    return (
      <MainLayout>
        <div className="flex min-h-screen justify-center items-center">
          <div className="text-center">
            <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
            <p className="mt-4 text-lg text-gray-600">Loading your subscriptions...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Only render content if authenticated
  if (!isAuthenticated) {
    return null;
  }

  return (
    <MainLayout>
      <div className="bg-gray-50 min-h-screen py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-12">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">My Subscriptions</h1>
              <p className="text-lg text-gray-600">
                Manage your wig subscriptions and delivery preferences
              </p>
            </div>

            {/* Error state */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Empty state */}
            {subscriptions.length === 0 && !isLoading && (
              <div className="text-center py-16">
                <svg className="mx-auto h-24 w-24 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="mt-6 text-xl font-medium text-gray-900">No subscriptions yet</h3>
                <p className="mt-2 text-gray-500">Start saving with automatic wig deliveries!</p>
                <div className="mt-8">
                  <button
                    onClick={() => router.push('/subscription')}
                    className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    Start a Subscription
                  </button>
                </div>
              </div>
            )}

            {/* Subscriptions */}
            {subscriptions.length > 0 && (
              <div className="space-y-6">
                <AnimatePresence>
                  {subscriptions.map((subscription) => (
                    <SubscriptionCard
                      key={subscription.id}
                      subscription={subscription}
                      onPause={() => handleAction(() => pauseSubscription(subscription.id), subscription.id)}
                      onResume={() => handleAction(() => resumeSubscription(subscription.id), subscription.id)}
                      onCancel={() => handleAction(() => cancelSubscription(subscription.id), subscription.id)}
                      onSkip={() => handleAction(() => skipNextDelivery(subscription.id), subscription.id)}
                      onEdit={() => router.push(`/account/subscriptions/${subscription.id}/edit`)}
                    />
                  ))}
                </AnimatePresence>
                
                {/* Add new subscription */}
                <div className="text-center pt-8">
                  <button
                    onClick={() => router.push('/subscription')}
                    className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add New Subscription
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
