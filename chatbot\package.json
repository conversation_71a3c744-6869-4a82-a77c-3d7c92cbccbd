{"name": "wig-ecommerce-chatbot", "version": "1.0.0", "description": "Headless chatbot core engine for wig e-commerce platform", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "lint": "eslint . --ext .ts"}, "dependencies": {"axios": "^1.6.0", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "express": "^4.18.2", "mongodb": "^5.8.1", "openai": "^4.15.0", "redis": "^4.6.10", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/express": "^4.17.20", "@types/jest": "^29.5.14", "@types/node": "^20.8.10", "@types/uuid": "^9.0.6", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "eslint": "^8.52.0", "jest": "^29.7.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}}