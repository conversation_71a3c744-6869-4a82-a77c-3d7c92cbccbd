const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  phone: {
    type: String,
    trim: true
  },
  dateOfBirth: Date,
  gender: {
    type: String,
    enum: ['male', 'female', 'other', 'prefer_not_to_say']
  },
  role: {
    type: String,
    enum: ['customer', 'admin', 'super_admin'],
    default: 'customer'
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active'
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  phoneVerified: {
    type: Boolean,
    default: false
  },
  addresses: [{
    type: {
      type: String,
      enum: ['shipping', 'billing', 'both'],
      default: 'both'
    },
    isDefault: {
      type: Boolean,
      default: false
    },
    firstName: String,
    lastName: String,
    company: String,
    street: {
      type: String,
      required: true
    },
    apartment: String,
    city: {
      type: String,
      required: true
    },
    state: {
      type: String,
      required: true
    },
    zipCode: {
      type: String,
      required: true
    },
    country: {
      type: String,
      required: true,
      default: 'United States'
    }
  }],
  preferences: {
    newsletter: {
      type: Boolean,
      default: true
    },
    smsMarketing: {
      type: Boolean,
      default: false
    },
    emailMarketing: {
      type: Boolean,
      default: true
    },
    currency: {
      type: String,
      default: 'USD'
    },
    language: {
      type: String,
      default: 'en'
    }
  },
  wishlist: [{
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product'
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  cart: [{
    productId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product'
    },
    quantity: {
      type: Number,
      default: 1,
      min: 1
    },
    selectedVariants: [{
      name: String,
      value: String
    }],
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  orderHistory: [{
    orderId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Order'
    },
    orderNumber: String,
    total: Number,
    status: String,
    orderDate: Date
  }],
  customerMetrics: {
    totalOrders: {
      type: Number,
      default: 0
    },
    totalSpent: {
      type: Number,
      default: 0
    },
    averageOrderValue: {
      type: Number,
      default: 0
    },
    lastOrderDate: Date,
    firstOrderDate: Date,
    lifetimeValue: {
      type: Number,
      default: 0
    }
  },
  socialProfiles: {
    instagram: String,
    facebook: String,
    twitter: String
  },
  loginHistory: [{
    ip: String,
    userAgent: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  resetPasswordToken: String,
  resetPasswordExpires: Date,
  emailVerificationToken: String,
  emailVerificationExpires: Date
}, {
  timestamps: true
});

// Indexes
userSchema.index({ email: 1 });
userSchema.index({ role: 1 });
userSchema.index({ status: 1 });
userSchema.index({ 'customerMetrics.totalSpent': -1 });
userSchema.index({ createdAt: -1 });

// Virtual for full name
userSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for customer segment
userSchema.virtual('customerSegment').get(function() {
  const totalSpent = this.customerMetrics.totalSpent;
  const totalOrders = this.customerMetrics.totalOrders;
  
  if (totalSpent >= 1000 || totalOrders >= 5) {
    return 'VIP';
  } else if (totalSpent >= 200 || totalOrders >= 2) {
    return 'Regular';
  } else {
    return 'New';
  }
});

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Pre-save middleware to update customer metrics
userSchema.pre('save', function(next) {
  if (this.customerMetrics.totalOrders > 0) {
    this.customerMetrics.averageOrderValue = this.customerMetrics.totalSpent / this.customerMetrics.totalOrders;
    this.customerMetrics.lifetimeValue = this.customerMetrics.totalSpent;
  }
  next();
});

// Instance method to compare password
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Instance method to add to wishlist
userSchema.methods.addToWishlist = function(productId) {
  const existingItem = this.wishlist.find(item => item.productId.toString() === productId.toString());
  if (!existingItem) {
    this.wishlist.push({ productId, addedAt: new Date() });
  }
  return this.save();
};

// Instance method to remove from wishlist
userSchema.methods.removeFromWishlist = function(productId) {
  this.wishlist = this.wishlist.filter(item => item.productId.toString() !== productId.toString());
  return this.save();
};

// Instance method to add to cart
userSchema.methods.addToCart = function(productId, quantity = 1, selectedVariants = []) {
  const existingItem = this.cart.find(item => 
    item.productId.toString() === productId.toString() &&
    JSON.stringify(item.selectedVariants) === JSON.stringify(selectedVariants)
  );
  
  if (existingItem) {
    existingItem.quantity += quantity;
  } else {
    this.cart.push({
      productId,
      quantity,
      selectedVariants,
      addedAt: new Date()
    });
  }
  return this.save();
};

// Instance method to update cart item
userSchema.methods.updateCartItem = function(productId, quantity, selectedVariants = []) {
  const item = this.cart.find(item => 
    item.productId.toString() === productId.toString() &&
    JSON.stringify(item.selectedVariants) === JSON.stringify(selectedVariants)
  );
  
  if (item) {
    if (quantity <= 0) {
      this.cart = this.cart.filter(cartItem => cartItem !== item);
    } else {
      item.quantity = quantity;
    }
  }
  return this.save();
};

// Instance method to clear cart
userSchema.methods.clearCart = function() {
  this.cart = [];
  return this.save();
};

// Instance method to update customer metrics after order
userSchema.methods.updateMetricsAfterOrder = function(orderTotal) {
  this.customerMetrics.totalOrders += 1;
  this.customerMetrics.totalSpent += orderTotal;
  this.customerMetrics.lastOrderDate = new Date();
  
  if (!this.customerMetrics.firstOrderDate) {
    this.customerMetrics.firstOrderDate = new Date();
  }
  
  return this.save();
};

// Static method to find customers with filters
userSchema.statics.findCustomersWithFilters = function(filters = {}) {
  const query = { role: 'customer' };
  
  if (filters.search) {
    query.$or = [
      { firstName: { $regex: filters.search, $options: 'i' } },
      { lastName: { $regex: filters.search, $options: 'i' } },
      { email: { $regex: filters.search, $options: 'i' } }
    ];
  }
  
  if (filters.status) {
    query.status = filters.status;
  }
  
  if (filters.segment) {
    switch (filters.segment) {
      case 'VIP':
        query.$or = [
          { 'customerMetrics.totalSpent': { $gte: 1000 } },
          { 'customerMetrics.totalOrders': { $gte: 5 } }
        ];
        break;
      case 'Regular':
        query.$and = [
          { 'customerMetrics.totalSpent': { $gte: 200, $lt: 1000 } },
          { 'customerMetrics.totalOrders': { $gte: 2, $lt: 5 } }
        ];
        break;
      case 'New':
        query.$and = [
          { 'customerMetrics.totalSpent': { $lt: 200 } },
          { 'customerMetrics.totalOrders': { $lt: 2 } }
        ];
        break;
    }
  }
  
  return this.find(query);
};

module.exports = mongoose.model('User', userSchema);
