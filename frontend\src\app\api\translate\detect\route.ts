import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { text } = await request.json();

    if (!text) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }

    // Try Google Translate API for language detection
    const googleApiKey = process.env.GOOGLE_TRANSLATE_API_KEY;
    
    if (googleApiKey) {
      try {
        const response = await fetch(
          `https://translation.googleapis.com/language/translate/v2/detect?key=${googleApiKey}`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              q: text
            })
          }
        );

        if (response.ok) {
          const data = await response.json();
          const detectedLanguage = data.data.detections[0][0].language;
          const confidence = data.data.detections[0][0].confidence;

          return NextResponse.json({
            language: detectedLanguage,
            confidence,
            service: 'google'
          });
        }
      } catch (error) {
        console.error('Google language detection error:', error);
      }
    }

    // Fallback: Simple language detection based on character patterns
    const detectedLanguage = detectLanguageSimple(text);

    return NextResponse.json({
      language: detectedLanguage,
      confidence: 0.7,
      service: 'simple'
    });

  } catch (error) {
    console.error('Language detection error:', error);
    return NextResponse.json(
      { error: 'Language detection failed' },
      { status: 500 }
    );
  }
}

// Simple language detection based on character patterns
function detectLanguageSimple(text: string): string {
  const cleanText = text.toLowerCase().trim();

  // Spanish indicators
  const spanishPatterns = [
    /\b(el|la|los|las|un|una|de|del|en|con|por|para|que|es|son|está|están)\b/g,
    /ñ/g,
    /[áéíóúü]/g
  ];

  // English indicators
  const englishPatterns = [
    /\b(the|and|or|but|in|on|at|to|for|of|with|by|from|is|are|was|were|have|has|had)\b/g,
    /\b(this|that|these|those|what|where|when|why|how)\b/g
  ];

  // Chinese indicators
  const chinesePatterns = [
    /[\u4e00-\u9fff]/g
  ];

  // Count matches for each language
  let spanishScore = 0;
  let englishScore = 0;
  let chineseScore = 0;

  spanishPatterns.forEach(pattern => {
    const matches = cleanText.match(pattern);
    if (matches) spanishScore += matches.length;
  });

  englishPatterns.forEach(pattern => {
    const matches = cleanText.match(pattern);
    if (matches) englishScore += matches.length;
  });

  chinesePatterns.forEach(pattern => {
    const matches = cleanText.match(pattern);
    if (matches) chineseScore += matches.length * 2; // Weight Chinese characters more
  });

  // Determine language based on highest score
  if (chineseScore > 0) return 'zh';
  if (spanishScore > englishScore) return 'es';
  if (englishScore > 0) return 'en';

  // Default to English if no clear indicators
  return 'en';
}
