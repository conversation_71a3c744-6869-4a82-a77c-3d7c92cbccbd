'use client';

import React, { useState, useCallback, memo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useCart } from '@/contexts/CartContext';
import { useWishlist } from '@/contexts/WishlistContext';
import { useAuth } from '@/contexts/AuthContext';
import { useComparison } from '@/contexts/ComparisonContext';

interface ColorOption {
  name: string;
  value: string;
}

interface ProductCardProps {
  id: string;
  slug: string;
  name: string;
  price: number;
  originalPrice?: number;
  category?: string;
  imageUrl: string;
  secondaryImageUrl?: string;
  rating: number;
  reviewCount: number;
  isNew?: boolean;
  isFeatured?: boolean;
  isWishlisted?: boolean;
  colorOptions?: ColorOption[];
}

const ProductCard: React.FC<ProductCardProps> = memo(({
  id,
  slug,
  name,
  price,
  originalPrice,
  category = "Wig",
  imageUrl,
  secondaryImageUrl,
  rating,
  reviewCount,
  isNew,
  isFeatured,
  isWishlisted,
  colorOptions
}) => {
  const { addItem } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const { isAuthenticated } = useAuth();
  const { addToComparison, removeFromComparison, isInComparison, canAddMore } = useComparison();
  const [isHovered, setIsHovered] = useState(false);
  const [isWishlistLoading, setIsWishlistLoading] = useState(false);
  const [isQuickViewOpen, setIsQuickViewOpen] = useState(false);
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [isSecondaryImageLoaded, setIsSecondaryImageLoaded] = useState(false);

  // Check if item is in wishlist and comparison
  const isItemInWishlist = isInWishlist(id);
  const isItemInComparison = isInComparison(id);
  
  // Handle adding to cart
  const handleAddToCart = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Add to cart using CartContext
    addItem({
      id,
      name,
      price,
      quantity: 1,
      image: imageUrl,
      // No attributes since specific variants aren't selected from the product card
    });
    
    // Optional: Show a notification for feedback
  }, [id, name, price, imageUrl, addItem]);
  
  // Handle adding to wishlist
  const handleToggleWishlist = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      alert('Please log in to add items to your wishlist');
      return;
    }

    setIsWishlistLoading(true);

    try {
      if (isItemInWishlist) {
        await removeFromWishlist(id);
      } else {
        await addToWishlist(id);
      }
    } catch (error) {
      console.error('Wishlist error:', error);
    } finally {
      setIsWishlistLoading(false);
    }
  }, [id, isItemInWishlist, isAuthenticated, addToWishlist, removeFromWishlist]);
  
  // Handle quick view
  const handleQuickView = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsQuickViewOpen(true);
    // Open quick view modal
    console.log('Quick view:', id);
  }, [id]);

  // Handle comparison toggle
  const handleComparisonToggle = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (isItemInComparison) {
      removeFromComparison(id);
    } else {
      if (!canAddMore) {
        alert('You can only compare up to 4 products at once. Please remove a product first.');
        return;
      }

      // Create comparison product object
      const comparisonProduct = {
        _id: id,
        name,
        price,
        originalPrice,
        images: [imageUrl],
        slug,
        rating,
        reviewCount,
        features: [], // These would come from the full product data
        specifications: {},
        category: category || 'Wig',
        tags: [],
        stock: 10 // Default stock
      };

      addToComparison(comparisonProduct);
    }
  }, [id, isItemInComparison, canAddMore, removeFromComparison, addToComparison, name, price, originalPrice, imageUrl, slug, rating, reviewCount, category]);
  
  // Calculate discounted price and sale badge
  const hasDiscount = originalPrice && originalPrice > price;
  const discountPercentage = hasDiscount
    ? Math.round(((originalPrice - price) / originalPrice) * 100)
    : 0;
    
  // Ensure the slug has the correct format
  const productLink = slug ? (slug.startsWith('/') ? slug : `/product/${slug}`) : '/';
    
  // Stars rendering for ratings
  const renderStars = useCallback((rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const halfStar = rating % 1 >= 0.5;
    
    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <svg key={i} className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        );
      } else if (i === fullStars && halfStar) {
        stars.push(
          <svg key={i} className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <defs>
              <linearGradient id="halfStarGradient">
                <stop offset="50%" stopColor="currentColor" />
                <stop offset="50%" stopColor="#D1D5DB" />
              </linearGradient>
            </defs>
            <path fill="url(#halfStarGradient)" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        );
      } else {
        stars.push(
          <svg key={i} className="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        );
      }
    }
    
    return stars;
  }, []);
  
  return (
    <motion.div
      className="group relative rounded-lg overflow-hidden bg-white shadow-md hover:shadow-xl transition-all duration-300"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      layout="position"
    >
      <Link href={productLink} prefetch={false}>
        <div className="block">
          {/* Product image with hover effect */}
          <div className="relative h-72 overflow-hidden bg-gray-100">
            {!isImageLoaded && (
              <div className="absolute inset-0 bg-gray-200 animate-pulse"></div>
            )}
            <Image
              src={imageUrl}
              alt={name}
              fill
              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
              style={{ objectFit: 'cover' }}
              className={`transform transition-transform duration-700 ${
                isHovered ? 'scale-110' : 'scale-100'
              } ${isImageLoaded ? 'opacity-100' : 'opacity-0'}`}
              priority={false}
              loading="lazy"
              onLoadingComplete={() => setIsImageLoaded(true)}
            />
            
            {/* Secondary image on hover (if available) */}
            {secondaryImageUrl && (
              <div 
                className={`absolute inset-0 transition-opacity duration-500 ${
                  isHovered && isSecondaryImageLoaded ? 'opacity-100' : 'opacity-0'
                }`}
              >
                <Image
                  src={secondaryImageUrl}
                  alt={`${name} - alternate view`}
                  fill
                  sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  style={{ objectFit: 'cover' }}
                  loading="lazy"
                  onLoadingComplete={() => setIsSecondaryImageLoaded(true)}
                />
              </div>
            )}
            
            {/* Quick action buttons on hover */}
            <div 
              className={`absolute bottom-0 left-0 right-0 px-4 py-3 bg-white/95 backdrop-blur-sm transform transition-transform duration-300 ${
                isHovered ? 'translate-y-0 shadow-md' : 'translate-y-full'
              }`}
            >
              <div className="flex justify-between items-center">
                <button
                  onClick={handleQuickView}
                  className="text-gray-700 text-sm font-medium hover:text-primary transition-colors duration-200 flex items-center"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  Quick View
                </button>
                <button
                  onClick={handleAddToCart}
                  className="text-primary hover:text-primary/80 transition-colors duration-200 flex items-center text-sm font-medium"
                >
                  <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  Add to Cart
                </button>
              </div>
            </div>
            
            {/* Action buttons container */}
            <div className="absolute top-3 right-3 flex flex-col gap-2">
              {/* Compare button */}
              <button
                onClick={handleComparisonToggle}
                className={`rounded-full p-2 transition-all duration-300 ${
                  isItemInComparison
                    ? 'bg-blue-500/20 shadow-md'
                    : 'bg-white/90 hover:bg-white shadow-sm hover:shadow-md'
                }`}
                aria-label={isItemInComparison ? "Remove from comparison" : "Add to comparison"}
              >
                {isItemInComparison ? (
                  <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5 text-gray-500 hover:text-blue-500 transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                )}
              </button>

              {/* Wishlist button */}
              <button
                onClick={handleToggleWishlist}
                disabled={isWishlistLoading}
                className={`rounded-full p-2 transition-all duration-300 ${
                  isItemInWishlist
                    ? 'bg-primary/20 shadow-md'
                    : 'bg-white/90 hover:bg-white shadow-sm hover:shadow-md'
                } ${isWishlistLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                aria-label={isItemInWishlist ? "Remove from wishlist" : "Add to wishlist"}
              >
                {isWishlistLoading ? (
                  <svg className="w-5 h-5 text-gray-500 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : isItemInWishlist ? (
                  <svg className="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5 text-gray-500 hover:text-primary transition-colors duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                )}
              </button>
            </div>
            
            {/* Badge container */}
            <div className="absolute top-3 left-3 flex flex-col gap-2">
              {/* Sale badge */}
              {hasDiscount && (
                <div className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-md shadow-sm">
                  {discountPercentage}% OFF
                </div>
              )}
              
              {/* New badge */}
              {isNew && (
                <div className="bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-md shadow-sm">
                  NEW
                </div>
              )}
              
              {/* Featured badge */}
              {isFeatured && (
                <div className="bg-purple-500 text-white text-xs font-bold px-2 py-1 rounded-md shadow-sm">
                  FEATURED
                </div>
              )}
            </div>
          </div>
          
          {/* Product info */}
          <div className="p-4">
            <div className="mb-2">
              <h3 className="text-sm font-medium text-primary mb-1">{category}</h3>
              <h2 className="text-base font-semibold text-gray-900 line-clamp-2 h-12">{name}</h2>
            </div>
            
            {/* Ratings */}
            <div className="flex items-center mb-2">
              <div className="flex">{renderStars(rating)}</div>
              <span className="ml-1 text-xs text-gray-500">({reviewCount})</span>
            </div>
            
            {/* Price */}
            <div className="flex items-center mb-3">
              <span className="text-lg font-bold text-gray-900">${typeof price === 'number' ? price.toFixed(2) : '0.00'}</span>
              {hasDiscount && originalPrice && (
                <span className="ml-2 text-sm font-medium text-gray-500 line-through">
                  ${typeof originalPrice === 'number' ? originalPrice.toFixed(2) : '0.00'}
                </span>
              )}
            </div>
            
            {/* Color options */}
            {colorOptions && colorOptions.length > 0 && (
              <div className="flex space-x-1">
                {colorOptions.map((color) => (
                  <div
                    key={color.value}
                    className="w-5 h-5 rounded-full border border-gray-200 shadow-sm cursor-pointer hover:scale-110 transition-transform duration-200"
                    style={{ backgroundColor: color.value }}
                    title={color.name}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </Link>
    </motion.div>
  );
});

ProductCard.displayName = 'ProductCard';

export default ProductCard; 