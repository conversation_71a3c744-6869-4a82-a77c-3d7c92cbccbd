'use client';

import React, { useState, useEffect } from 'react';
import RangeSlider from '@/components/ui/RangeSlider';
import Checkbox from '@/components/ui/Checkbox';
import RadioGroup from '@/components/ui/RadioGroup';
import ColorSwatch from '@/components/ui/ColorSwatch';
import Accordion from '@/components/ui/Accordion';
import EnhancedColorSwatch from './EnhancedColorSwatch';
import SizeGuide from './SizeGuide';
import FilterPresets from './FilterPresets';
import FilterHistory from './FilterHistory';

// Available colors for wigs
const COLORS = [
  { id: 'black', name: 'Black', value: '#000000' },
  { id: 'darkBrown', name: '<PERSON> Brown', value: '#3b2314' },
  { id: 'mediumBrown', name: '<PERSON> <PERSON>', value: '#654321' },
  { id: 'lightBrown', name: '<PERSON> Brown', value: '#A67B5B' },
  { id: 'blonde', name: '<PERSON>lon<PERSON>', value: '#E5C8A8' },
  { id: 'platinum', name: 'Platinum Blonde', value: '#E6E6FA' },
  { id: 'auburn', name: 'Auburn', value: '#922724' },
  { id: 'copper', name: 'Copper', value: '#CB6D51' },
  { id: 'red', name: 'Red', value: '#9B2335' },
  { id: 'burgundy', name: 'Burgundy', value: '#8D021F' },
  { id: 'purple', name: 'Purple', value: '#800080' },
  { id: 'blue', name: 'Blue', value: '#4169E1' },
  { id: 'pink', name: 'Pink', value: '#FF69B4' },
  { id: 'grey', name: 'Grey/Silver', value: '#ACACAC' },
  { id: 'ombre', name: 'Ombré', value: 'linear-gradient(180deg, #3b2314 0%, #E5C8A8 100%)' },
  { id: 'highlights', name: 'Highlights', value: 'linear-gradient(90deg, #3b2314 50%, #A67B5B 50%)' },
];

// Lengths for wigs
const LENGTHS = [
  { id: 'short', name: 'Short (8-12")' },
  { id: 'medium', name: 'Medium (14-18")' },
  { id: 'long', name: 'Long (20-24")' },
  { id: 'extraLong', name: 'Extra Long (26"+)' },
];

// Styles for wigs
const STYLES = [
  { id: 'straight', name: 'Straight' },
  { id: 'wavy', name: 'Wavy' },
  { id: 'curly', name: 'Curly' },
  { id: 'kinky', name: 'Kinky/Coily' },
  { id: 'body-wave', name: 'Body Wave' },
  { id: 'deep-wave', name: 'Deep Wave' },
  { id: 'water-wave', name: 'Water Wave' },
  { id: 'loose-wave', name: 'Loose Wave' },
  { id: 'kinky-straight', name: 'Kinky Straight' },
  { id: 'afro', name: 'Afro' },
];

// Density options
const DENSITIES = [
  { id: 'light', name: '110%-130% (Light)' },
  { id: 'medium', name: '150%-180% (Medium)' },
  { id: 'heavy', name: '200%-250% (Full)' },
];

// Cap construction types
const CAP_CONSTRUCTIONS = [
  { id: 'lace-front', name: 'Lace Front' },
  { id: 'full-lace', name: 'Full Lace' },
  { id: '360-lace', name: '360° Lace' },
  { id: 'hd-lace', name: 'HD Lace' },
  { id: 'transparent-lace', name: 'Transparent Lace' },
  { id: 'capless', name: 'Capless / Wefted' },
  { id: 'monofilament', name: 'Monofilament' },
  { id: 'hand-tied', name: 'Hand-Tied' },
  { id: 'glueless', name: 'Glueless' },
];

// Material quality options
const MATERIALS = [
  { id: 'human-remy', name: 'Human Hair (Remy)' },
  { id: 'human-non-remy', name: 'Human Hair (Non-Remy)' },
  { id: 'human-virgin', name: 'Virgin Human Hair' },
  { id: 'synthetic', name: 'Synthetic' },
  { id: 'heat-friendly-synthetic', name: 'Heat-Friendly Synthetic' },
  { id: 'blend', name: 'Human-Synthetic Blend' },
];

interface Color {
  id: string;
  name: string;
  value: string;
}

interface Option {
  id: string;
  name: string;
}

interface Filters {
  priceRange: [number, number];
  colors: string[];
  lengths: string[];
  styles: string[];
  density: string[];
  capConstruction: string[];
  material: string[];
}

interface FilterPanelProps {
  filters: Filters;
  onFilterChange: (filterType: keyof Filters, value: any) => void;
  onClearFilters: () => void;
  isMobile?: boolean;
  className?: string;
}

const FilterPanel: React.FC<FilterPanelProps> = ({ 
  filters, 
  onFilterChange, 
  onClearFilters, 
  isMobile = false, 
  className = '' 
}) => {
  const [localPriceRange, setLocalPriceRange] = useState<[number, number]>(filters.priceRange);
  const [isSizeGuideOpen, setIsSizeGuideOpen] = useState(false);
  
  // Update local state when the parent's filters change
  useEffect(() => {
    setLocalPriceRange(filters.priceRange);
  }, [filters.priceRange]);
  
  // Handle price range changes
  const handlePriceChange = (newRange: [number, number]) => {
    setLocalPriceRange(newRange);
  };
  
  // Only update parent filter after user stops dragging
  const handlePriceChangeComplete = (newRange: [number, number]) => {
    onFilterChange('priceRange', newRange);
  };
  
  // Handle color selection
  const handleColorChange = (colorId: string) => {
    const currentColors = [...filters.colors];
    const index = currentColors.indexOf(colorId);
    
    if (index === -1) {
      // Add color if not already selected
      onFilterChange('colors', [...currentColors, colorId]);
    } else {
      // Remove color if already selected
      onFilterChange('colors', currentColors.filter(id => id !== colorId));
    }
  };
  
  // Handle checkbox changes for multi-select filters
  const handleCheckboxChange = (filterType: keyof Filters, itemId: string) => {
    const currentValues = [...filters[filterType] as string[]];
    const index = currentValues.indexOf(itemId);
    
    if (index === -1) {
      // Add value if not already selected
      onFilterChange(filterType, [...currentValues, itemId]);
    } else {
      // Remove value if already selected
      onFilterChange(filterType, currentValues.filter(id => id !== itemId));
    }
  };
  
  // Handle manual input for price range
  const handleMinPriceInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Math.max(0, Number(e.target.value));
    if (!isNaN(value)) {
      setLocalPriceRange([value, localPriceRange[1]]);
    }
  };
  
  const handleMaxPriceInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Math.max(0, Number(e.target.value));
    if (!isNaN(value)) {
      setLocalPriceRange([localPriceRange[0], value]);
    }
  };
  
  const handlePriceInputBlur = () => {
    onFilterChange('priceRange', localPriceRange);
  };
  
  return (
    <div className={`${className} ${isMobile ? 'pb-16' : ''}`}>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-medium text-gray-900">Filters</h3>
        <button
          onClick={onClearFilters}
          className="text-sm text-indigo-600 hover:text-indigo-800"
        >
          Clear All
        </button>
      </div>

      {/* Filter Presets */}
      <div className="mb-6">
        <FilterPresets onApplyPreset={(presetFilters) => {
          Object.entries(presetFilters).forEach(([key, value]) => {
            onFilterChange(key as keyof Filters, value);
          });
        }} />
      </div>

      {/* Filter History */}
      <div className="mb-6">
        <FilterHistory
          currentFilters={filters}
          onApplyFilters={(historyFilters) => {
            Object.entries(historyFilters).forEach(([key, value]) => {
              onFilterChange(key as keyof Filters, value);
            });
          }}
        />
      </div>
      
      {/* Price Range Filter */}
      <Accordion title="Price Range" defaultOpen={true}>
        <div className="py-4">
          <RangeSlider
            min={0}
            max={1000}
            step={10}
            values={localPriceRange}
            onChange={handlePriceChange}
            onChangeComplete={handlePriceChangeComplete}
          />
          <div className="mt-4 flex justify-between items-center">
            <div className="relative rounded-md shadow-sm w-24">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="number"
                min="0"
                className="block w-full pl-7 pr-2 py-1 sm:text-sm border border-gray-300 rounded-md"
                placeholder="Min"
                value={localPriceRange[0]}
                onChange={handleMinPriceInput}
                onBlur={handlePriceInputBlur}
              />
            </div>
            <span className="text-gray-500 mx-2">to</span>
            <div className="relative rounded-md shadow-sm w-24">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">$</span>
              </div>
              <input
                type="number"
                min="0"
                className="block w-full pl-7 pr-2 py-1 sm:text-sm border border-gray-300 rounded-md"
                placeholder="Max"
                value={localPriceRange[1]}
                onChange={handleMaxPriceInput}
                onBlur={handlePriceInputBlur}
              />
            </div>
          </div>
        </div>
      </Accordion>
      
      {/* Color Filter */}
      <Accordion title="Color" defaultOpen={true}>
        <div className="pt-2 pb-4">
          <div className="grid grid-cols-4 gap-3">
            {COLORS.map((color) => (
              <EnhancedColorSwatch
                key={color.id}
                color={color.value}
                name={color.name}
                selected={filters.colors.includes(color.id)}
                onClick={() => handleColorChange(color.id)}
                size="md"
                showName={false}
              />
            ))}
          </div>
          <div className="mt-3 text-center">
            <p className="text-xs text-gray-500">
              {filters.colors.length > 0
                ? `${filters.colors.length} color${filters.colors.length > 1 ? 's' : ''} selected`
                : 'Select colors to filter'
              }
            </p>
          </div>
        </div>
      </Accordion>
      
      {/* Length Filter */}
      <Accordion title="Length" defaultOpen={false}>
        <div className="py-4">
          <div className="flex justify-between items-center mb-3">
            <span className="text-sm text-gray-600">Select hair length</span>
            <button
              onClick={() => setIsSizeGuideOpen(true)}
              className="text-xs text-primary hover:text-primary/80 font-medium flex items-center space-x-1"
            >
              <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Size Guide</span>
            </button>
          </div>
          <div className="space-y-2">
            {LENGTHS.map((length) => (
              <Checkbox
                key={length.id}
                id={`length-${length.id}`}
                label={length.name}
                checked={filters.lengths.includes(length.id)}
                onChange={() => handleCheckboxChange('lengths', length.id)}
              />
            ))}
          </div>
        </div>
      </Accordion>
      
      {/* Style Filter */}
      <Accordion title="Style" defaultOpen={false}>
        <div className="py-4 space-y-2">
          {STYLES.map((style) => (
            <Checkbox
              key={style.id}
              id={`style-${style.id}`}
              label={style.name}
              checked={filters.styles.includes(style.id)}
              onChange={() => handleCheckboxChange('styles', style.id)}
            />
          ))}
        </div>
      </Accordion>
      
      {/* Density Filter */}
      <Accordion title="Density" defaultOpen={false}>
        <div className="py-4 space-y-2">
          {DENSITIES.map((density) => (
            <Checkbox
              key={density.id}
              id={`density-${density.id}`}
              label={density.name}
              checked={filters.density.includes(density.id)}
              onChange={() => handleCheckboxChange('density', density.id)}
            />
          ))}
        </div>
      </Accordion>
      
      {/* Cap Construction Filter */}
      <Accordion title="Cap Construction" defaultOpen={false}>
        <div className="py-4 space-y-2">
          {CAP_CONSTRUCTIONS.map((cap) => (
            <Checkbox
              key={cap.id}
              id={`cap-${cap.id}`}
              label={cap.name}
              checked={filters.capConstruction.includes(cap.id)}
              onChange={() => handleCheckboxChange('capConstruction', cap.id)}
            />
          ))}
        </div>
      </Accordion>
      
      {/* Material Filter */}
      <Accordion title="Material" defaultOpen={false}>
        <div className="py-4 space-y-2">
          {MATERIALS.map((material) => (
            <Checkbox
              key={material.id}
              id={`material-${material.id}`}
              label={material.name}
              checked={filters.material.includes(material.id)}
              onChange={() => handleCheckboxChange('material', material.id)}
            />
          ))}
        </div>
      </Accordion>

      {/* Size Guide Modal */}
      <SizeGuide
        isOpen={isSizeGuideOpen}
        onClose={() => setIsSizeGuideOpen(false)}
      />
    </div>
  );
};

export default FilterPanel; 