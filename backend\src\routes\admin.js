const express = require('express');
const router = express.Router();

// Import controllers
const apiKeysController = require('../controllers/apiKeysController');
const productsController = require('../controllers/productsController');
const ordersController = require('../controllers/ordersController');
const customersController = require('../controllers/customersController');
const analyticsController = require('../controllers/analyticsController');

// Middleware for admin authentication (placeholder)
const requireAdmin = (req, res, next) => {
  // This should be replaced with actual admin authentication
  // For now, we'll assume the user is authenticated and is an admin
  req.user = { id: 'admin-user-id', role: 'admin' };
  next();
};

// API Keys routes
router.get('/api-keys', requireAdmin, apiKeysController.getApiKeys);
router.put('/api-keys/:keyId', requireAdmin, apiKeysController.updateApiKey);
router.post('/api-keys/:keyId/test', requireAdmin, apiKeysController.testApiKey);

// Products routes
router.get('/products', requireAdmin, productsController.getProducts);
router.get('/products/analytics', requireAdmin, productsController.getProductAnalytics);
router.get('/products/:id', requireAdmin, productsController.getProduct);
router.post('/products', requireAdmin, productsController.createProduct);
router.put('/products/:id', requireAdmin, productsController.updateProduct);
router.delete('/products/:id', requireAdmin, productsController.deleteProduct);
router.put('/products/bulk-update', requireAdmin, productsController.bulkUpdateProducts);
router.post('/products/sync-aliexpress', requireAdmin, productsController.syncWithAliExpress);

// Orders routes
router.get('/orders', requireAdmin, ordersController.getOrders);
router.get('/orders/analytics', requireAdmin, ordersController.getOrderAnalytics);
router.get('/orders/export', requireAdmin, ordersController.exportOrders);
router.get('/orders/:id', requireAdmin, ordersController.getOrder);
router.put('/orders/:id/status', requireAdmin, ordersController.updateOrderStatus);
router.put('/orders/:id/fulfillment', requireAdmin, ordersController.updateOrderFulfillment);
router.post('/orders/:id/communications', requireAdmin, ordersController.addOrderCommunication);

// Customers routes
router.get('/customers', requireAdmin, customersController.getCustomers);
router.get('/customers/analytics', requireAdmin, customersController.getCustomerAnalytics);
router.get('/customers/export', requireAdmin, customersController.exportCustomers);
router.get('/customers/:id', requireAdmin, customersController.getCustomer);
router.put('/customers/:id', requireAdmin, customersController.updateCustomer);

// Analytics routes
router.get('/analytics/dashboard', requireAdmin, analyticsController.getDashboardOverview);
router.get('/analytics/sales', requireAdmin, analyticsController.getSalesAnalytics);
router.get('/analytics/customers', requireAdmin, analyticsController.getCustomerAnalytics);
router.get('/analytics/products', requireAdmin, analyticsController.getProductAnalytics);

module.exports = router;
