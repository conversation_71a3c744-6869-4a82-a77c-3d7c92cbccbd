'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface ComparisonProduct {
  _id: string;
  name: string;
  price: number;
  originalPrice?: number;
  images: string[];
  slug: string;
  rating: number;
  reviewCount: number;
  features: string[];
  specifications: Record<string, any>;
  category: string;
  tags: string[];
  stock: number;
}

interface ComparisonContextType {
  comparisonList: ComparisonProduct[];
  isLoading: boolean;
  error: string | null;
  addToComparison: (product: ComparisonProduct) => void;
  removeFromComparison: (productId: string) => void;
  clearComparison: () => void;
  isInComparison: (productId: string) => boolean;
  canAddMore: boolean;
  maxItems: number;
}

const ComparisonContext = createContext<ComparisonContextType | undefined>(undefined);

export const useComparison = () => {
  const context = useContext(ComparisonContext);
  if (context === undefined) {
    throw new Error('useComparison must be used within a ComparisonProvider');
  }
  return context;
};

interface ComparisonProviderProps {
  children: ReactNode;
}

const MAX_COMPARISON_ITEMS = 4; // Maximum number of products to compare
const STORAGE_KEY = 'product_comparison';

export const ComparisonProvider: React.FC<ComparisonProviderProps> = ({ children }) => {
  const [comparisonList, setComparisonList] = useState<ComparisonProduct[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load comparison list from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        setComparisonList(parsed);
      }
    } catch (err) {
      console.error('Error loading comparison list from localStorage:', err);
    }
  }, []);

  // Save comparison list to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(comparisonList));
    } catch (err) {
      console.error('Error saving comparison list to localStorage:', err);
    }
  }, [comparisonList]);

  const addToComparison = (product: ComparisonProduct) => {
    setError(null);
    
    // Check if already in comparison
    if (isInComparison(product._id)) {
      setError('Product is already in comparison');
      return;
    }
    
    // Check if we've reached the maximum
    if (comparisonList.length >= MAX_COMPARISON_ITEMS) {
      setError(`You can only compare up to ${MAX_COMPARISON_ITEMS} products at once`);
      return;
    }
    
    setComparisonList(prev => [...prev, product]);
  };

  const removeFromComparison = (productId: string) => {
    setError(null);
    setComparisonList(prev => prev.filter(item => item._id !== productId));
  };

  const clearComparison = () => {
    setError(null);
    setComparisonList([]);
  };

  const isInComparison = (productId: string): boolean => {
    return comparisonList.some(item => item._id === productId);
  };

  const canAddMore = comparisonList.length < MAX_COMPARISON_ITEMS;

  const value: ComparisonContextType = {
    comparisonList,
    isLoading,
    error,
    addToComparison,
    removeFromComparison,
    clearComparison,
    isInComparison,
    canAddMore,
    maxItems: MAX_COMPARISON_ITEMS,
  };

  return (
    <ComparisonContext.Provider value={value}>
      {children}
    </ComparisonContext.Provider>
  );
};
