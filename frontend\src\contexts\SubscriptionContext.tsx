'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { 
  subscriptionService, 
  Subscription, 
  SubscriptionPlan, 
  CreateSubscriptionData, 
  UpdateSubscriptionData 
} from '@/services/subscriptionService';

interface SubscriptionContextType {
  subscriptions: Subscription[];
  plans: SubscriptionPlan[];
  isLoading: boolean;
  error: string | null;
  createSubscription: (data: CreateSubscriptionData) => Promise<Subscription>;
  updateSubscription: (subscriptionId: string, data: UpdateSubscriptionData) => Promise<Subscription>;
  pauseSubscription: (subscriptionId: string) => Promise<void>;
  resumeSubscription: (subscriptionId: string) => Promise<void>;
  cancelSubscription: (subscriptionId: string) => Promise<void>;
  skipNextDelivery: (subscriptionId: string) => Promise<void>;
  refreshSubscriptions: () => Promise<void>;
  refreshPlans: () => Promise<void>;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

interface SubscriptionProviderProps {
  children: ReactNode;
}

export const SubscriptionProvider: React.FC<SubscriptionProviderProps> = ({ children }) => {
  const { isAuthenticated, user } = useAuth();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load subscriptions and plans when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      refreshSubscriptions();
      refreshPlans();
    } else {
      setSubscriptions([]);
      setPlans([]);
    }
  }, [isAuthenticated, user]);

  const refreshSubscriptions = async () => {
    if (!isAuthenticated) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await subscriptionService.getUserSubscriptions();
      setSubscriptions(data);
    } catch (err: any) {
      setError(err.message || 'Failed to load subscriptions');
      console.error('Error loading subscriptions:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshPlans = async () => {
    setError(null);
    
    try {
      const data = await subscriptionService.getPlans();
      setPlans(data);
    } catch (err: any) {
      setError(err.message || 'Failed to load subscription plans');
      console.error('Error loading subscription plans:', err);
    }
  };

  const createSubscription = async (data: CreateSubscriptionData): Promise<Subscription> => {
    if (!isAuthenticated) {
      throw new Error('Please log in to create a subscription');
    }

    setError(null);
    
    try {
      const newSubscription = await subscriptionService.createSubscription(data);
      setSubscriptions(prev => [...prev, newSubscription]);
      return newSubscription;
    } catch (err: any) {
      setError(err.message || 'Failed to create subscription');
      throw err;
    }
  };

  const updateSubscription = async (subscriptionId: string, data: UpdateSubscriptionData): Promise<Subscription> => {
    if (!isAuthenticated) {
      throw new Error('Please log in to update subscription');
    }

    setError(null);
    
    try {
      const updatedSubscription = await subscriptionService.updateSubscription(subscriptionId, data);
      setSubscriptions(prev => 
        prev.map(sub => sub.id === subscriptionId ? updatedSubscription : sub)
      );
      return updatedSubscription;
    } catch (err: any) {
      setError(err.message || 'Failed to update subscription');
      throw err;
    }
  };

  const pauseSubscription = async (subscriptionId: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Please log in to pause subscription');
    }

    setError(null);
    
    try {
      const updatedSubscription = await subscriptionService.pauseSubscription(subscriptionId);
      setSubscriptions(prev => 
        prev.map(sub => sub.id === subscriptionId ? updatedSubscription : sub)
      );
    } catch (err: any) {
      setError(err.message || 'Failed to pause subscription');
      throw err;
    }
  };

  const resumeSubscription = async (subscriptionId: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Please log in to resume subscription');
    }

    setError(null);
    
    try {
      const updatedSubscription = await subscriptionService.resumeSubscription(subscriptionId);
      setSubscriptions(prev => 
        prev.map(sub => sub.id === subscriptionId ? updatedSubscription : sub)
      );
    } catch (err: any) {
      setError(err.message || 'Failed to resume subscription');
      throw err;
    }
  };

  const cancelSubscription = async (subscriptionId: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Please log in to cancel subscription');
    }

    setError(null);
    
    try {
      await subscriptionService.cancelSubscription(subscriptionId);
      setSubscriptions(prev => 
        prev.map(sub => 
          sub.id === subscriptionId 
            ? { ...sub, status: 'cancelled' as const }
            : sub
        )
      );
    } catch (err: any) {
      setError(err.message || 'Failed to cancel subscription');
      throw err;
    }
  };

  const skipNextDelivery = async (subscriptionId: string): Promise<void> => {
    if (!isAuthenticated) {
      throw new Error('Please log in to skip delivery');
    }

    setError(null);
    
    try {
      const updatedSubscription = await subscriptionService.skipNextDelivery(subscriptionId);
      setSubscriptions(prev => 
        prev.map(sub => sub.id === subscriptionId ? updatedSubscription : sub)
      );
    } catch (err: any) {
      setError(err.message || 'Failed to skip delivery');
      throw err;
    }
  };

  const value: SubscriptionContextType = {
    subscriptions,
    plans,
    isLoading,
    error,
    createSubscription,
    updateSubscription,
    pauseSubscription,
    resumeSubscription,
    cancelSubscription,
    skipNextDelivery,
    refreshSubscriptions,
    refreshPlans,
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
};
