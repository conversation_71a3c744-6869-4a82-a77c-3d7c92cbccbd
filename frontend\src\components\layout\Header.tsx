'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '@/contexts/ThemeContext';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { useWishlist } from '@/contexts/WishlistContext';
import { Menu, Transition } from '@headlessui/react';
import { 
  StarIcon, 
  ShippingIcon, 
  DiamondIcon, 
  ReturnIcon, 
  HeartIcon, 
  QualityIcon, 
  FireIcon, 
  GiftIcon, 
  MoneyIcon, 
  ScissorsIcon, 
  ArrowRightIcon,
  CheckIcon,
  UsersIcon 
} from '@/components/ui/Icons';

const navigation = [
  { 
    name: 'Home', 
    href: '/',
    description: 'Latest collections and trends'
  },
  { 
    name: 'Shop', 
    href: '/shop',
    description: 'Browse all wigs',
    submenu: [
      { name: '<PERSON>e Front Wigs', href: '/shop/lace-front', description: 'Natural hairline' },
      { name: 'Full Lace Wigs', href: '/shop/full-lace', description: '360° styling' },
      { name: 'Bob Wigs', href: '/shop/bob', description: 'Chic & trendy' },
      { name: 'Curly Wigs', href: '/shop/curly', description: 'Beautiful textures' },
      { name: 'Blonde Wigs', href: '/shop/blonde', description: 'Light & luminous' },
      { name: 'Long Wigs', href: '/shop/long', description: 'Flowing styles' }
    ]
  },
  { 
    name: 'Catalog', 
    href: '/catalog',
    description: 'Complete product catalog'
  },
  { 
    name: 'Collections', 
    href: '/collections',
    description: 'Curated collections'
  },
  { 
    name: 'Blog', 
    href: '/blog',
    description: 'Tips, trends & tutorials'
  },
  { 
    name: 'About', 
    href: '/about',
    description: 'Our story & values'
  },
  { 
    name: 'Contact', 
    href: '/contact',
    description: 'Get in touch with us'
  }
];

const languages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'es', name: 'Español', flag: '🇪🇸' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'de', name: 'Deutsch', flag: '🇩🇪' }
];

const searchSuggestions = [
  'Lace front wigs',
  'Bob wigs',
  'Curly hair wigs',
  'Blonde wigs',
  'Long hair wigs',
  'Human hair wigs',
  'Synthetic wigs',
  'Short wigs'
];

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState(languages[0]);
  const [showCartPreview, setShowCartPreview] = useState(false);
  const [showNotification, setShowNotification] = useState(true);
  
  const pathname = usePathname();
  const { theme, toggleTheme } = useTheme();
  const { itemCount, items } = useCart();
  const { isAuthenticated, user, logout } = useAuth();
  const { wishlist } = useWishlist();
  
  const searchRef = useRef<HTMLDivElement>(null);

  // Handle scroll effect with opacity change
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const isScrolled = scrollPosition > 10;
      setScrolled(isScrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close search on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setSearchOpen(false);
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter search suggestions
  const filteredSuggestions = searchSuggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;
    }
  };

  // Close mobile menu when window resizes to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <>
      {/* New Items Notification Banner */}
      <AnimatePresence>
        {showNotification && (
          <motion.div
            initial={{ y: -50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -50, opacity: 0 }}
            className="bg-gradient-to-r from-primary via-secondary to-accent text-white py-2 text-center text-sm font-medium relative z-50"
          >
            <div className="flex items-center justify-center space-x-2">
              <FireIcon className="w-4 h-4 text-yellow-300" />
              <span>New arrivals just dropped! Free shipping on orders over $200</span>
              <FireIcon className="w-4 h-4 text-yellow-300" />
          </div>
            <button
              onClick={() => setShowNotification(false)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white/80 hover:text-white"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Premium Navigation Bar */}
      <motion.header
        className={`fixed left-0 right-0 z-40 transition-all duration-500 ${
          scrolled 
            ? 'bg-white/80 backdrop-blur-lg shadow-xl border-b border-gradient-to-r from-primary/20 to-secondary/20' 
            : 'bg-white/60 backdrop-blur-md'
        }`}
        style={{
          top: showNotification ? '40px' : '0px',
          backdropFilter: 'blur(20px)',
          borderImage: scrolled ? 'linear-gradient(90deg, #E75A97 0%, #9A73E4 100%) 1' : 'none'
        }}
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Enhanced Logo */}
            <motion.div 
              className="flex-shrink-0"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <Link href="/" className="flex items-center space-x-4">
                <motion.div
                  className="relative w-12 h-12 bg-gradient-to-br from-primary via-secondary to-accent rounded-xl flex items-center justify-center shadow-lg"
                  whileHover={{ 
                    rotate: [0, -5, 5, 0],
                    scale: 1.05
                  }}
                  transition={{ duration: 0.6 }}
                >
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-xl"
                    animate={{ 
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 0.8, 0.5]
                    }}
                    transition={{ 
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <DiamondIcon className="w-7 h-7 text-white relative z-10" />
                </motion.div>
                <div className="flex flex-col">
                  <motion.span 
                    className="font-display text-2xl font-bold bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent"
                    whileHover={{ scale: 1.02 }}
                  >
                    Pelucas Chic
                  </motion.span>
                  <span className="text-xs text-gray-500 font-medium tracking-wide">
                    Premium Human Hair
                  </span>
                </div>
              </Link>
            </motion.div>

            {/* Desktop Navigation with Advanced Effects */}
            <nav className="hidden lg:flex items-center space-x-1">
              {navigation.map((item, index) => (
                <motion.div
                  key={item.name}
                  className="relative"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.5 }}
                  onMouseEnter={() => item.submenu && setActiveSubmenu(item.name)}
                  onMouseLeave={() => setActiveSubmenu(null)}
                >
                  <Link href={item.href}>
                    <motion.div
                      className="relative px-6 py-3 text-sm font-medium text-gray-700 transition-all duration-300 group cursor-pointer"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <span className="relative z-10 group-hover:text-white transition-colors duration-300 flex items-center space-x-1">
                        <span>{item.name}</span>
                        {item.submenu && (
                          <motion.svg 
                            className="w-4 h-4"
                            animate={{ rotate: activeSubmenu === item.name ? 180 : 0 }}
                            transition={{ duration: 0.3 }}
                            fill="none" 
                            stroke="currentColor" 
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </motion.svg>
                        )}
                      </span>
                      
                      {/* Active page indicator */}
                      <motion.div
                        className="absolute -bottom-1 left-1/2 w-2 h-2 bg-gradient-to-r from-primary to-secondary rounded-full opacity-0"
                        animate={{ 
                          opacity: pathname === item.href ? 1 : 0,
                          x: '-50%'
                        }}
                      />
                      
                      {/* Hover background */}
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-primary to-secondary rounded-lg opacity-0"
                        whileHover={{ opacity: 1 }}
                        transition={{ duration: 0.3 }}
                      />
                      
                      {/* Animated underline */}
                      <motion.div
                        className="absolute bottom-0 left-1/2 w-0 h-0.5 bg-gradient-to-r from-primary to-secondary"
                        whileHover={{ width: '80%', x: '-50%' }}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                      />
                    </motion.div>
                  </Link>

                  {/* Advanced Dropdown Menu */}
                  <AnimatePresence>
                    {item.submenu && activeSubmenu === item.name && (
                      <motion.div
                        initial={{ opacity: 0, y: 10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: 10, scale: 0.95 }}
                        transition={{ duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }}
                        className="absolute top-full left-0 mt-2 w-80 bg-white/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-100 overflow-hidden"
                      >
                        <div className="p-6">
                          <div className="grid grid-cols-1 gap-3">
                            {item.submenu.map((subitem, subIndex) => (
                              <motion.div
                                key={subitem.name}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: subIndex * 0.05 }}
                              >
                                <Link href={subitem.href}>
                                  <motion.div
                                    className="flex items-start space-x-3 p-3 rounded-xl hover:bg-gradient-to-r hover:from-primary/5 hover:to-secondary/5 group transition-all duration-300"
                                    whileHover={{ x: 5 }}
                                  >
                                    <div className="w-2 h-2 bg-gradient-to-r from-primary to-secondary rounded-full mt-2 opacity-60 group-hover:opacity-100" />
                                    <div>
                                      <h4 className="font-medium text-gray-900 group-hover:text-primary transition-colors">
                                        {subitem.name}
                                      </h4>
                                      <p className="text-sm text-gray-500 group-hover:text-gray-600">
                                        {subitem.description}
                                      </p>
                                    </div>
                                  </motion.div>
                                </Link>
                              </motion.div>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </nav>

            {/* Enhanced Right Section */}
            <div className="hidden lg:flex items-center space-x-2">
              {/* Advanced Search */}
              <div ref={searchRef} className="relative">
                <motion.button
                  onClick={() => setSearchOpen(!searchOpen)}
                  className="p-3 rounded-full text-gray-600 hover:text-primary focus:outline-none relative group transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.div
                    className="absolute inset-0 bg-primary/10 rounded-full opacity-0"
                    whileHover={{ opacity: 1 }}
                  />
                  <svg className="w-5 h-5 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </motion.button>

                {/* Expandable Search */}
                <AnimatePresence>
                  {searchOpen && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8, x: 20 }}
                      animate={{ opacity: 1, scale: 1, x: 0 }}
                      exit={{ opacity: 0, scale: 0.8, x: 20 }}
                      transition={{ duration: 0.3 }}
                      className="absolute right-0 top-full mt-2 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-100 overflow-hidden"
                    >
                      <form onSubmit={handleSearchSubmit} className="p-4">
                        <div className="relative">
                          <input
                            type="text"
                            placeholder="Search for wigs..."
                            value={searchQuery}
                            onChange={(e) => {
                              setSearchQuery(e.target.value);
                              setShowSuggestions(e.target.value.length > 0);
                            }}
                            className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary text-gray-900"
                            autoFocus
                          />
                          <button
                            type="submit"
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 bg-gradient-to-r from-primary to-secondary text-white rounded-lg hover:shadow-lg transition-all duration-300"
                          >
                            <ArrowRightIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </form>

                      {/* Search Suggestions */}
                      <AnimatePresence>
                        {showSuggestions && filteredSuggestions.length > 0 && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="border-t border-gray-100"
                          >
                            <div className="p-2 max-h-60 overflow-y-auto">
                              {filteredSuggestions.slice(0, 6).map((suggestion, index) => (
                                <motion.button
                                  key={suggestion}
                                  initial={{ opacity: 0, x: -10 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ delay: index * 0.05 }}
                                  onClick={() => {
                                    setSearchQuery(suggestion);
                                    setShowSuggestions(false);
                                  }}
                                  className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-primary/5 hover:text-primary rounded-lg transition-all duration-200"
                                >
                                  {suggestion}
                                </motion.button>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Language Selector */}
              <Menu as="div" className="relative">
                <Menu.Button as={motion.button}
                  className="p-3 rounded-full text-gray-600 hover:text-primary focus:outline-none relative group transition-all duration-300"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <motion.div
                    className="absolute inset-0 bg-primary/10 rounded-full opacity-0"
                    whileHover={{ opacity: 1 }}
                  />
                  <div className="flex items-center space-x-1 relative z-10">
                    <span className="text-lg">{currentLanguage.flag}</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                  </div>
                </Menu.Button>
                <Transition
                  enter="transition ease-out duration-200"
                  enterFrom="opacity-0 scale-95"
                  enterTo="opacity-100 scale-100"
                  leave="transition ease-in duration-150"
                  leaveFrom="opacity-100 scale-100"
                  leaveTo="opacity-0 scale-95"
                >
                  <Menu.Items className="absolute right-0 mt-2 w-48 origin-top-right rounded-xl bg-white/95 backdrop-blur-xl shadow-2xl ring-1 ring-black/5 border border-gray-100 focus:outline-none overflow-hidden">
                    <div className="p-2">
                      {languages.map((language) => (
                        <Menu.Item key={language.code}>
                      {({ active }) => (
                            <motion.button
                              onClick={() => setCurrentLanguage(language)}
                              className={`flex items-center space-x-3 w-full px-3 py-2 text-sm rounded-lg transition-all duration-200 ${
                                active ? 'bg-primary/10 text-primary' : 'text-gray-700'
                              } ${currentLanguage.code === language.code ? 'bg-primary/5' : ''}`}
                              whileHover={{ x: 3 }}
                            >
                              <span className="text-lg">{language.flag}</span>
                              <span>{language.name}</span>
                              {currentLanguage.code === language.code && (
                                <CheckIcon className="w-4 h-4 ml-auto text-primary" />
                              )}
                            </motion.button>
                      )}
                    </Menu.Item>
                      ))}
                    </div>
                  </Menu.Items>
                </Transition>
              </Menu>

              {/* Theme Toggle */}
              <motion.button
                onClick={toggleTheme}
                className="p-3 rounded-full text-gray-600 hover:text-primary focus:outline-none relative group transition-all duration-300"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <motion.div
                  className="absolute inset-0 bg-primary/10 rounded-full opacity-0"
                  whileHover={{ opacity: 1 }}
                />
                <motion.div
                  animate={{ rotate: theme === 'dark' ? 180 : 0 }}
                  transition={{ duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }}
                  className="relative z-10"
                >
                  {theme === 'dark' ? (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                    </svg>
                  )}
                </motion.div>
              </motion.button>

              {/* User Account */}
              {isAuthenticated ? (
                <Menu as="div" className="relative">
                  <Menu.Button as={motion.button}
                    className="flex items-center space-x-2 p-3 rounded-full text-gray-600 hover:text-primary focus:outline-none relative group transition-all duration-300"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <motion.div
                      className="absolute inset-0 bg-primary/10 rounded-full opacity-0"
                      whileHover={{ opacity: 1 }}
                    />
                    <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center relative z-10">
                      <span className="text-white text-sm font-medium">
                        {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                      </span>
                    </div>
                    <span className="hidden xl:block text-sm font-medium relative z-10">
                      {user?.name?.split(' ')[0] || 'Account'}
                    </span>
                  </Menu.Button>
                  <Transition
                    enter="transition ease-out duration-200"
                    enterFrom="opacity-0 scale-95"
                    enterTo="opacity-100 scale-100"
                    leave="transition ease-in duration-150"
                    leaveFrom="opacity-100 scale-100"
                    leaveTo="opacity-0 scale-95"
                  >
                    <Menu.Items className="absolute right-0 mt-2 w-56 origin-top-right rounded-xl bg-white/95 backdrop-blur-xl shadow-2xl ring-1 ring-black/5 border border-gray-100 focus:outline-none overflow-hidden">
                      <div className="p-2">
                        {[
                          { label: 'My Account', href: '/account', icon: UsersIcon },
                          { label: 'Orders', href: '/account/orders', icon: ShippingIcon },
                          { label: 'Wishlist', href: '/account/wishlist', icon: HeartIcon },
                          { label: 'Settings', href: '/account/settings', icon: QualityIcon }
                        ].map((item) => (
                          <Menu.Item key={item.label}>
                      {({ active }) => (
                              <Link href={item.href}>
                                <motion.div
                                  className={`flex items-center space-x-3 px-3 py-2 text-sm rounded-lg transition-all duration-200 ${
                                    active ? 'bg-primary/10 text-primary' : 'text-gray-700'
                                  }`}
                                  whileHover={{ x: 3 }}
                                >
                                  <item.icon className="w-4 h-4" />
                                  <span>{item.label}</span>
                                </motion.div>
                        </Link>
                      )}
                    </Menu.Item>
                        ))}
                        <div className="border-t border-gray-200 my-2"></div>
                    <Menu.Item>
                      {({ active }) => (
                            <motion.button
                          onClick={logout}
                              className={`flex items-center space-x-3 w-full px-3 py-2 text-sm rounded-lg transition-all duration-200 ${
                                active ? 'bg-red-50 text-red-600' : 'text-gray-700'
                              }`}
                              whileHover={{ x: 3 }}
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                              </svg>
                              <span>Sign Out</span>
                            </motion.button>
                      )}
                    </Menu.Item>
                      </div>
                  </Menu.Items>
                </Transition>
              </Menu>
            ) : (
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
              <Link
                href="/auth/login"
                    className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white font-medium rounded-full hover:shadow-lg transition-all duration-300"
                  >
                    <UsersIcon className="w-4 h-4" />
                    <span>Sign In</span>
              </Link>
                </motion.div>
            )}

              {/* Wishlist */}
              {isAuthenticated && (
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                  <Link
                    href="/wishlist"
                    className="relative group"
                  >
                    <motion.div
                      className="flex items-center space-x-2 px-4 py-3 bg-gradient-to-r from-pink-500/10 to-red-500/10 hover:from-pink-500/20 hover:to-red-500/20 rounded-full border border-pink-500/20 hover:border-pink-500/40 transition-all duration-300"
                      whileHover={{ scale: 1.02 }}
                    >
                      <HeartIcon className="w-5 h-5 text-pink-500" />
                      <span className="text-sm font-medium text-pink-500">Wishlist</span>
                      <AnimatePresence>
                        {wishlist.length > 0 && (
                          <motion.div
                            className="relative"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            exit={{ scale: 0 }}
                          >
                            <motion.span
                              className="bg-gradient-to-r from-pink-500 to-red-500 text-white text-xs font-bold rounded-full min-w-[20px] h-5 flex items-center justify-center px-1.5 shadow-lg border-2 border-white"
                              animate={{
                                scale: [1, 1.2, 1],
                                rotate: [0, 10, -10, 0]
                              }}
                              transition={{
                                duration: 0.6,
                                repeat: Infinity,
                                repeatDelay: 3
                              }}
                            >
                              {wishlist.length}
                            </motion.span>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  </Link>
                </motion.div>
              )}

              {/* Enhanced Cart with Preview */}
              <div 
                className="relative"
                onMouseEnter={() => setShowCartPreview(true)}
                onMouseLeave={() => setShowCartPreview(false)}
              >
                <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Link
              href="/cart"
                    className="relative group"
                  >
                    <motion.div 
                      className="flex items-center space-x-2 px-4 py-3 bg-gradient-to-r from-primary/10 to-secondary/10 hover:from-primary/20 hover:to-secondary/20 rounded-full border border-primary/20 hover:border-primary/40 transition-all duration-300"
                      whileHover={{ scale: 1.02 }}
            >
                      <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 7a2 2 0 01-2 2H8a2 2 0 01-2-2L5 9z" />
              </svg>
                      <span className="text-sm font-medium text-primary">Cart</span>
                      <AnimatePresence>
              {itemCount > 0 && (
                          <motion.div
                            className="relative"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            exit={{ scale: 0 }}
                          >
                            <motion.span
                              className="bg-gradient-to-r from-primary to-secondary text-white text-xs font-bold rounded-full min-w-[20px] h-5 flex items-center justify-center px-1.5 shadow-lg border-2 border-white"
                              animate={{ 
                                scale: [1, 1.2, 1],
                                rotate: [0, 10, -10, 0]
                              }}
                              transition={{ 
                                duration: 0.6,
                                repeat: Infinity,
                                repeatDelay: 3
                              }}
                            >
                  {itemCount}
                            </motion.span>
                          </motion.div>
              )}
                      </AnimatePresence>
                    </motion.div>
                  </Link>
                </motion.div>

                {/* Cart Preview */}
                <AnimatePresence>
                  {showCartPreview && itemCount > 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: 10, scale: 0.95 }}
                      transition={{ duration: 0.3 }}
                      className="absolute right-0 top-full mt-2 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-100 overflow-hidden"
                    >
                      <div className="p-4">
                        <h3 className="font-medium text-gray-900 mb-3">Shopping Cart ({itemCount} items)</h3>
                        <div className="space-y-3 max-h-60 overflow-y-auto">
                          {items?.slice(0, 3).map((item: any, index: number) => (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.1 }}
                              className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50"
                            >
                              <div className="w-12 h-12 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-lg flex items-center justify-center">
                                <DiamondIcon className="w-6 h-6 text-primary" />
                              </div>
                              <div className="flex-1">
                                <h4 className="text-sm font-medium text-gray-900 truncate">
                                  {item.name || 'Premium Wig'}
                                </h4>
                                <p className="text-xs text-gray-500">
                                  Qty: {item.quantity} • ${item.price}
                                </p>
                              </div>
                            </motion.div>
                          ))}
                        </div>
                        {items?.length > 3 && (
                          <p className="text-sm text-gray-500 text-center mt-3">
                            +{items.length - 3} more items
                          </p>
                        )}
                        <Link href="/cart">
                          <motion.button
                            className="w-full mt-4 px-4 py-2 bg-gradient-to-r from-primary to-secondary text-white font-medium rounded-lg hover:shadow-lg transition-all duration-300"
                            whileHover={{ scale: 1.02 }}
                          >
                            View Cart
                          </motion.button>
            </Link>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
          </div>

            {/* Mobile Menu Button */}
            <motion.button
              className="lg:hidden p-3 rounded-full text-gray-600 hover:text-primary focus:outline-none relative"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                className="absolute inset-0 bg-primary/10 rounded-full opacity-0"
                whileHover={{ opacity: 1 }}
              />
              <motion.div
                animate={{ rotate: isMenuOpen ? 180 : 0 }}
                transition={{ duration: 0.3 }}
                className="relative z-10"
            >
              {isMenuOpen ? (
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : (
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              )}
              </motion.div>
            </motion.button>
        </div>
      </div>

        {/* Mobile Menu Overlay */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              className="lg:hidden fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
                  onClick={() => setIsMenuOpen(false)}
                >
              <motion.div
                className="absolute right-0 top-0 w-80 h-full bg-white/95 backdrop-blur-xl shadow-2xl"
                initial={{ x: '100%' }}
                animate={{ x: 0 }}
                exit={{ x: '100%' }}
                transition={{ type: 'spring', damping: 25, stiffness: 200 }}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="p-6 h-full overflow-y-auto">
                  {/* Mobile Header */}
                  <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
                        <DiamondIcon className="w-5 h-5 text-white" />
            </div>
                      <span className="font-display text-lg font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                        Pelucas Chic
                      </span>
                    </div>
                    <motion.button
                      onClick={() => setIsMenuOpen(false)}
                      className="p-2 rounded-full text-gray-600 hover:text-primary"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </motion.button>
                  </div>

                  {/* Mobile Navigation */}
                  <div className="space-y-2 mb-8">
                    {navigation.map((item, index) => (
                      <motion.div
                        key={item.name}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                    <Link
                          href={item.href}
                          className="block px-4 py-3 text-base font-medium text-gray-700 hover:text-primary hover:bg-primary/5 rounded-lg transition-all duration-200"
                      onClick={() => setIsMenuOpen(false)}
                    >
                          {item.name}
                    </Link>
                      </motion.div>
                    ))}
                </div>

                  {/* Mobile Actions */}
                  <div className="space-y-4 border-t border-gray-200 pt-6">
                    {!isAuthenticated && (
                      <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.3 }}
                      >
                    <Link
                          href="/auth/login"
                          className="block w-full px-4 py-3 bg-gradient-to-r from-primary to-secondary text-white text-center font-medium rounded-lg"
                      onClick={() => setIsMenuOpen(false)}
                    >
                          Sign In
                    </Link>
                      </motion.div>
                    )}

                    {/* Mobile Wishlist */}
                    {isAuthenticated && (
                      <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.4 }}
                      >
                        <Link
                          href="/wishlist"
                          className="flex items-center justify-between px-4 py-3 text-base font-medium text-gray-700 hover:text-pink-500 hover:bg-pink-50 rounded-lg transition-all duration-200"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <div className="flex items-center space-x-3">
                            <HeartIcon className="w-5 h-5 text-pink-500" />
                            <span>Wishlist</span>
                          </div>
                          {wishlist.length > 0 && (
                            <span className="bg-gradient-to-r from-pink-500 to-red-500 text-white text-sm px-2.5 py-1 rounded-full font-medium">
                              {wishlist.length}
                            </span>
                          )}
                        </Link>
                      </motion.div>
                    )}

                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5 }}
                    >
                <Link
                  href="/cart"
                        className="flex items-center justify-between px-4 py-3 text-base font-medium text-gray-700 hover:text-primary hover:bg-primary/5 rounded-lg transition-all duration-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                        <div className="flex items-center space-x-3">
                          <svg className="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 7a2 2 0 01-2 2H8a2 2 0 01-2-2L5 9z" />
                  </svg>
                          <span>Shopping Cart</span>
                        </div>
                  {itemCount > 0 && (
                          <span className="bg-gradient-to-r from-primary to-secondary text-white text-sm px-2.5 py-1 rounded-full font-medium">
                      {itemCount}
                    </span>
                  )}
                </Link>
                    </motion.div>
              </div>
            </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.header>
    </>
  );
} 