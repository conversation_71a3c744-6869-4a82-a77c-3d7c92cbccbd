'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface SizeGuideProps {
  isOpen: boolean;
  onClose: () => void;
}

const sizeData = [
  {
    size: 'Petite',
    circumference: '20.5" - 21"',
    description: 'Perfect for smaller head sizes',
    measurements: {
      front: '11.5"',
      back: '11.5"',
      side: '11"',
      nape: '1.5"'
    }
  },
  {
    size: 'Average',
    circumference: '21.5" - 22"',
    description: 'Most common size, fits majority',
    measurements: {
      front: '12"',
      back: '12"',
      side: '11.5"',
      nape: '1.75"'
    }
  },
  {
    size: 'Large',
    circumference: '22.5" - 23"',
    description: 'For larger head circumferences',
    measurements: {
      front: '12.5"',
      back: '12.5"',
      side: '12"',
      nape: '2"'
    }
  }
];

const SizeGuide: React.FC<SizeGuideProps> = ({ isOpen, onClose }) => {
  const [selectedSize, setSelectedSize] = useState<string | null>(null);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-2xl font-bold text-gray-900">Wig Size Guide</h2>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* How to measure section */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">How to Measure Your Head</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold">
                        1
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">Circumference</h4>
                        <p className="text-sm text-gray-600">Measure around your head at the hairline, from forehead to nape of neck.</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold">
                        2
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">Front to Back</h4>
                        <p className="text-sm text-gray-600">Measure from your hairline to the nape of your neck.</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold">
                        3
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">Ear to Ear</h4>
                        <p className="text-sm text-gray-600">Measure from ear to ear across the top of your head.</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="aspect-square bg-white rounded-lg flex items-center justify-center">
                      <svg className="w-32 h-32 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                    <p className="text-sm text-gray-600 text-center mt-2">
                      Use a soft measuring tape for accurate measurements
                    </p>
                  </div>
                </div>
              </div>

              {/* Size chart */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Size Chart</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {sizeData.map((size) => (
                    <motion.div
                      key={size.size}
                      className={`border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                        selectedSize === size.size
                          ? 'border-primary bg-primary/5'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedSize(selectedSize === size.size ? null : size.size)}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="text-center mb-3">
                        <h4 className="text-lg font-semibold text-gray-900">{size.size}</h4>
                        <p className="text-primary font-medium">{size.circumference}</p>
                        <p className="text-sm text-gray-600">{size.description}</p>
                      </div>
                      
                      <AnimatePresence>
                        {selectedSize === size.size && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            className="border-t border-gray-200 pt-3 mt-3"
                          >
                            <h5 className="font-medium text-gray-900 mb-2">Detailed Measurements:</h5>
                            <div className="space-y-1 text-sm">
                              <div className="flex justify-between">
                                <span className="text-gray-600">Front to Back:</span>
                                <span className="font-medium">{size.measurements.front}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Side to Side:</span>
                                <span className="font-medium">{size.measurements.side}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-gray-600">Nape of Neck:</span>
                                <span className="font-medium">{size.measurements.nape}</span>
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Tips section */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-blue-900 mb-3">Sizing Tips</h3>
                <ul className="space-y-2 text-sm text-blue-800">
                  <li className="flex items-start space-x-2">
                    <svg className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Most wigs have adjustable straps for a custom fit</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <svg className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>If between sizes, choose the larger size for comfort</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <svg className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>Contact us for custom sizing if needed</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                onClick={onClose}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
              >
                Close
              </button>
              <button
                onClick={onClose}
                className="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors duration-200"
              >
                Got it!
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SizeGuide;
