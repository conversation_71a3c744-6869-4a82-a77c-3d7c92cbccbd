const User = require('../models/User');
const Order = require('../models/Order');

// Get all customers with filters and pagination
const getCustomers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      segment,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const filters = { role: 'customer' };
    
    if (search) {
      filters.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      filters.status = status;
    }
    
    if (segment) {
      switch (segment) {
        case 'VIP':
          filters.$or = [
            { 'customerMetrics.totalSpent': { $gte: 1000 } },
            { 'customerMetrics.totalOrders': { $gte: 5 } }
          ];
          break;
        case 'Regular':
          filters.$and = [
            { 'customerMetrics.totalSpent': { $gte: 200, $lt: 1000 } },
            { 'customerMetrics.totalOrders': { $gte: 2, $lt: 5 } }
          ];
          break;
        case 'New':
          filters.$and = [
            { 'customerMetrics.totalSpent': { $lt: 200 } },
            { 'customerMetrics.totalOrders': { $lt: 2 } }
          ];
          break;
      }
    }

    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [customers, total] = await Promise.all([
      User.find(filters)
        .select('-password -resetPasswordToken -emailVerificationToken')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      User.countDocuments(filters)
    ]);

    // Add customer segment to each customer
    const customersWithSegment = customers.map(customer => ({
      ...customer,
      segment: getCustomerSegment(customer.customerMetrics)
    }));

    const totalPages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      data: {
        customers: customersWithSegment,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: total,
          itemsPerPage: parseInt(limit),
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });
  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customers',
      error: error.message
    });
  }
};

// Get single customer by ID
const getCustomer = async (req, res) => {
  try {
    const { id } = req.params;
    
    const customer = await User.findById(id)
      .select('-password -resetPasswordToken -emailVerificationToken')
      .populate('wishlist.productId', 'name images price')
      .populate('cart.productId', 'name images price')
      .lean();
    
    if (!customer || customer.role !== 'customer') {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    // Get customer's order history
    const orders = await Order.find({ 'customer.userId': id })
      .sort({ createdAt: -1 })
      .limit(10)
      .select('orderNumber pricing.total orderStatus createdAt')
      .lean();

    // Add segment information
    customer.segment = getCustomerSegment(customer.customerMetrics);
    customer.recentOrders = orders;

    res.json({
      success: true,
      data: customer
    });
  } catch (error) {
    console.error('Error fetching customer:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer',
      error: error.message
    });
  }
};

// Update customer
const updateCustomer = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    // Remove sensitive fields that shouldn't be updated via this endpoint
    delete updateData.password;
    delete updateData.role;
    delete updateData.customerMetrics;
    
    const customer = await User.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password -resetPasswordToken -emailVerificationToken');
    
    if (!customer || customer.role !== 'customer') {
      return res.status(404).json({
        success: false,
        message: 'Customer not found'
      });
    }

    res.json({
      success: true,
      message: 'Customer updated successfully',
      data: customer
    });
  } catch (error) {
    console.error('Error updating customer:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update customer',
      error: error.message
    });
  }
};

// Get customer analytics
const getCustomerAnalytics = async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    let dateFilter = {};
    const now = new Date();
    
    switch (timeframe) {
      case '7d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } };
        break;
      case '30d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } };
        break;
      case '90d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) } };
        break;
    }

    const [
      totalCustomers,
      newCustomers,
      activeCustomers,
      customersBySegment,
      customersByStatus,
      topCustomers,
      customerGrowth,
      averageMetrics
    ] = await Promise.all([
      User.countDocuments({ role: 'customer' }),
      User.countDocuments({ role: 'customer', ...dateFilter }),
      User.countDocuments({ 
        role: 'customer', 
        'customerMetrics.lastOrderDate': { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) }
      }),
      User.aggregate([
        { $match: { role: 'customer' } },
        { $addFields: {
          segment: {
            $cond: {
              if: { $or: [
                { $gte: ['$customerMetrics.totalSpent', 1000] },
                { $gte: ['$customerMetrics.totalOrders', 5] }
              ]},
              then: 'VIP',
              else: {
                $cond: {
                  if: { $and: [
                    { $gte: ['$customerMetrics.totalSpent', 200] },
                    { $gte: ['$customerMetrics.totalOrders', 2] }
                  ]},
                  then: 'Regular',
                  else: 'New'
                }
              }
            }
          }
        }},
        { $group: { _id: '$segment', count: { $sum: 1 } } }
      ]),
      User.aggregate([
        { $match: { role: 'customer' } },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]),
      User.find({ role: 'customer' })
        .sort({ 'customerMetrics.totalSpent': -1 })
        .limit(10)
        .select('firstName lastName email customerMetrics')
        .lean(),
      User.aggregate([
        { $match: { role: 'customer' } },
        { $group: {
          _id: { 
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }},
        { $sort: { '_id.year': 1, '_id.month': 1 } },
        { $limit: 12 }
      ]),
      User.aggregate([
        { $match: { role: 'customer' } },
        { $group: {
          _id: null,
          avgTotalSpent: { $avg: '$customerMetrics.totalSpent' },
          avgTotalOrders: { $avg: '$customerMetrics.totalOrders' },
          avgOrderValue: { $avg: '$customerMetrics.averageOrderValue' }
        }}
      ])
    ]);

    // Add segment information to top customers
    const topCustomersWithSegment = topCustomers.map(customer => ({
      ...customer,
      segment: getCustomerSegment(customer.customerMetrics)
    }));

    res.json({
      success: true,
      data: {
        overview: {
          totalCustomers,
          newCustomers,
          activeCustomers,
          retentionRate: totalCustomers > 0 ? (activeCustomers / totalCustomers * 100).toFixed(1) : 0
        },
        customersBySegment,
        customersByStatus,
        topCustomers: topCustomersWithSegment,
        customerGrowth,
        averageMetrics: averageMetrics[0] || {
          avgTotalSpent: 0,
          avgTotalOrders: 0,
          avgOrderValue: 0
        }
      }
    });
  } catch (error) {
    console.error('Error fetching customer analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer analytics',
      error: error.message
    });
  }
};

// Export customers to CSV
const exportCustomers = async (req, res) => {
  try {
    const { 
      status,
      segment,
      format = 'csv'
    } = req.query;

    const filters = { role: 'customer' };
    
    if (status) {
      filters.status = status;
    }
    
    if (segment) {
      switch (segment) {
        case 'VIP':
          filters.$or = [
            { 'customerMetrics.totalSpent': { $gte: 1000 } },
            { 'customerMetrics.totalOrders': { $gte: 5 } }
          ];
          break;
        case 'Regular':
          filters.$and = [
            { 'customerMetrics.totalSpent': { $gte: 200, $lt: 1000 } },
            { 'customerMetrics.totalOrders': { $gte: 2, $lt: 5 } }
          ];
          break;
        case 'New':
          filters.$and = [
            { 'customerMetrics.totalSpent': { $lt: 200 } },
            { 'customerMetrics.totalOrders': { $lt: 2 } }
          ];
          break;
      }
    }

    const customers = await User.find(filters)
      .select('firstName lastName email phone customerMetrics status createdAt')
      .lean();

    if (format === 'csv') {
      const csv = convertToCSV(customers);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=customers.csv');
      res.send(csv);
    } else {
      res.json({
        success: true,
        data: customers
      });
    }
  } catch (error) {
    console.error('Error exporting customers:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export customers',
      error: error.message
    });
  }
};

// Helper function to determine customer segment
const getCustomerSegment = (metrics) => {
  const totalSpent = metrics?.totalSpent || 0;
  const totalOrders = metrics?.totalOrders || 0;
  
  if (totalSpent >= 1000 || totalOrders >= 5) {
    return 'VIP';
  } else if (totalSpent >= 200 || totalOrders >= 2) {
    return 'Regular';
  } else {
    return 'New';
  }
};

// Helper function to convert customers to CSV
const convertToCSV = (customers) => {
  const headers = ['First Name', 'Last Name', 'Email', 'Phone', 'Total Spent', 'Total Orders', 'Status', 'Segment', 'Join Date'];
  const rows = customers.map(customer => [
    customer.firstName,
    customer.lastName,
    customer.email,
    customer.phone || '',
    customer.customerMetrics?.totalSpent || 0,
    customer.customerMetrics?.totalOrders || 0,
    customer.status,
    getCustomerSegment(customer.customerMetrics),
    customer.createdAt.toISOString().split('T')[0]
  ]);
  
  return [headers, ...rows].map(row => row.join(',')).join('\n');
};

module.exports = {
  getCustomers,
  getCustomer,
  updateCustomer,
  getCustomerAnalytics,
  exportCustomers
};
