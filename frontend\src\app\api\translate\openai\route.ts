import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const { text, targetLanguage, sourceLanguage, context } = await request.json();

    if (!text || !targetLanguage) {
      return NextResponse.json(
        { error: 'Text and target language are required' },
        { status: 400 }
      );
    }

    // Create context-aware prompts
    const contextPrompts = {
      product: `Translate this product name to ${targetLanguage}. Keep it natural and appealing for e-commerce. Maintain brand names and technical terms where appropriate.`,
      description: `Translate this product description to ${targetLanguage}. Maintain the marketing tone and technical specifications. Make it sound natural and compelling for customers.`,
      category: `Translate this product category to ${targetLanguage}. Use standard e-commerce category naming conventions.`,
      general: `Translate this text to ${targetLanguage}. Maintain the original tone and meaning.`
    };

    const systemPrompt = contextPrompts[context as keyof typeof contextPrompts] || contextPrompts.general;

    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `You are a professional translator specializing in e-commerce content. ${systemPrompt} Only return the translated text, no explanations or additional content.`
        },
        {
          role: "user",
          content: text
        }
      ],
      max_tokens: 1000,
      temperature: 0.3,
    });

    const translatedText = completion.choices[0]?.message?.content?.trim();

    if (!translatedText) {
      throw new Error('No translation received from OpenAI');
    }

    return NextResponse.json({
      translatedText,
      confidence: 0.95, // OpenAI generally provides high-quality translations
      service: 'openai'
    });

  } catch (error) {
    console.error('OpenAI translation error:', error);
    return NextResponse.json(
      { error: 'Translation failed' },
      { status: 500 }
    );
  }
}
