'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  KeyIcon, 
  UserIcon, 
  LockClosedIcon,
  EyeIcon,
  EyeSlashIcon,
  ClipboardDocumentIcon
} from '@heroicons/react/24/outline';

export default function AdminCredentialsPage() {
  const [showPassword, setShowPassword] = React.useState(false);
  const [copied, setCopied] = React.useState(false);

  const credentials = {
    email: process.env.NEXT_PUBLIC_INITIAL_ADMIN_EMAIL || '<EMAIL>',
    password: process.env.NEXT_PUBLIC_INITIAL_ADMIN_PASSWORD || 'PelucasChic2024!',
    setupMode: process.env.NEXT_PUBLIC_INITIAL_SETUP_MODE || 'true'
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-white to-secondary/10 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full border border-gray-200"
      >
        {/* Header */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2 }}
            className="w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <KeyIcon className="w-8 h-8 text-white" />
          </motion.div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Admin Credentials</h1>
          <p className="text-gray-600">Use these credentials to access the admin dashboard</p>
        </div>

        {/* Credentials */}
        <div className="space-y-6">
          {/* Email */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="space-y-2"
          >
            <label className="flex items-center text-sm font-medium text-gray-700">
              <UserIcon className="w-4 h-4 mr-2" />
              Email
            </label>
            <div className="relative">
              <input
                type="email"
                value={credentials.email}
                readOnly
                className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-900 font-mono text-sm"
              />
              <button
                onClick={() => copyToClipboard(credentials.email)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary transition-colors"
              >
                <ClipboardDocumentIcon className="w-5 h-5" />
              </button>
            </div>
          </motion.div>

          {/* Password */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-2"
          >
            <label className="flex items-center text-sm font-medium text-gray-700">
              <LockClosedIcon className="w-4 h-4 mr-2" />
              Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={credentials.password}
                readOnly
                className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-900 font-mono text-sm pr-20"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
                <button
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-gray-400 hover:text-primary transition-colors"
                >
                  {showPassword ? (
                    <EyeSlashIcon className="w-5 h-5" />
                  ) : (
                    <EyeIcon className="w-5 h-5" />
                  )}
                </button>
                <button
                  onClick={() => copyToClipboard(credentials.password)}
                  className="text-gray-400 hover:text-primary transition-colors"
                >
                  <ClipboardDocumentIcon className="w-5 h-5" />
                </button>
              </div>
            </div>
          </motion.div>

          {/* Setup Mode Status */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
            className="p-4 bg-blue-50 border border-blue-200 rounded-lg"
          >
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-900">Setup Mode</span>
              <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                credentials.setupMode === 'true' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {credentials.setupMode === 'true' ? 'ENABLED' : 'DISABLED'}
              </span>
            </div>
            <p className="text-sm text-blue-700 mt-2">
              {credentials.setupMode === 'true' 
                ? 'Initial setup mode is enabled. You can create the admin account.'
                : 'Setup mode is disabled. Admin account should already exist.'
              }
            </p>
          </motion.div>
        </div>

        {/* Copy Notification */}
        {copied && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="mt-4 p-3 bg-green-100 border border-green-200 rounded-lg text-center"
          >
            <p className="text-sm text-green-800 font-medium">Copied to clipboard!</p>
          </motion.div>
        )}

        {/* Action Buttons */}
        <div className="mt-8 space-y-3">
          <motion.a
            href="/auth/login"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="block w-full bg-gradient-to-r from-primary to-secondary text-white text-center py-3 rounded-lg font-semibold hover:from-primary-dark hover:to-secondary-dark transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            Go to Login Page
          </motion.a>

          {credentials.setupMode === 'true' && (
            <motion.a
              href="/auth/admin-setup"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="block w-full border-2 border-primary text-primary text-center py-3 rounded-lg font-semibold hover:bg-primary hover:text-white transition-all duration-200"
            >
              Complete Admin Setup
            </motion.a>
          )}

          <motion.a
            href="/"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="block w-full text-gray-600 text-center py-2 hover:text-primary transition-colors"
          >
            Back to Homepage
          </motion.a>
        </div>

        {/* Instructions */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="mt-8 p-4 bg-gray-50 rounded-lg"
        >
          <h3 className="text-sm font-semibold text-gray-900 mb-2">Instructions:</h3>
          <ol className="text-xs text-gray-600 space-y-1">
            <li>1. Copy the email and password above</li>
            <li>2. Go to the login page</li>
            <li>3. Enter the credentials</li>
            <li>4. Access the admin dashboard</li>
          </ol>
        </motion.div>

        {/* Warning */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2 }}
          className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
        >
          <p className="text-xs text-yellow-800">
            <strong>Security Note:</strong> Change these credentials after first login for production use.
          </p>
        </motion.div>
      </motion.div>
    </div>
  );
}
