'use client';

import React, { useState, useEffect, useRef, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import ProductCard from '@/components/catalog/ProductCard';
import { AliExpressCatalogService, LocalProduct } from '@/services/aliexpressCatalog';
import LoadingSkeleton from '@/components/catalog/LoadingSkeleton';
import EmptyState from '@/components/catalog/EmptyState';
import ErrorState from '@/components/catalog/ErrorState';
import { motion } from 'framer-motion';

const PAGE_SIZE = 12;
const STORE_IDS = ['3497011', '4758009'];

interface StoreInfo {
  id: string;
  name: string;
  description: string;
}

const STORE_INFO: Record<string, StoreInfo> = {
  '3497011': {
    id: '3497011',
    name: 'Premium Hair Collection',
    description: 'High-quality wigs and hair extensions',
  },
  '4758009': {
    id: '4758009',
    name: 'Elite Beauty Store',
    description: 'Professional beauty and hair products',
  },
};

function PremiumCollectionsContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [products, setProducts] = useState<LocalProduct[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const [selectedStore, setSelectedStore] = useState<string>('all');
  const [connectionStatus, setConnectionStatus] = useState<'testing' | 'success' | 'failed' | null>(null);

  // Get store from URL params
  useEffect(() => {
    const storeFromUrl = searchParams?.get('store') || 'all';
    setSelectedStore(storeFromUrl);
  }, [searchParams]);

  // Test API connection on mount
  useEffect(() => {
    testApiConnection();
  }, []);

  // Fetch products when dependencies change
  useEffect(() => {
    if (connectionStatus === 'success') {
      fetchProducts();
    }
  }, [selectedStore, currentPage, connectionStatus]);

  const testApiConnection = async () => {
    setConnectionStatus('testing');
    try {
      const response = await AliExpressCatalogService.testConnection();
      if (response.success) {
        setConnectionStatus('success');
        console.log('✅ Catalog connection successful');
      } else {
        setConnectionStatus('failed');
        setError(response.error || 'Catalog connection failed');
      }
    } catch (error) {
      setConnectionStatus('failed');
      setError('Failed to connect to product catalog');
      console.error('❌ Catalog connection test failed:', error);
    }
  };

  const fetchProducts = async () => {
    setIsLoading(true);
    setError(null);

    try {
      let response;
      
      if (selectedStore === 'all') {
        console.log('🏬 Fetching products from all collections');
        response = await AliExpressCatalogService.getAllStoreProducts(currentPage, PAGE_SIZE);
      } else {
        console.log('🏪 Fetching products from collection:', selectedStore);
        response = await AliExpressCatalogService.getStoreProducts(selectedStore, currentPage, PAGE_SIZE);
      }

      if (response.success) {
        const localProducts = AliExpressCatalogService.convertResponseToLocalProducts(response);
        setProducts(localProducts);
        setTotalProducts(response.totalProducts || localProducts.length);
        console.log('📦 Products loaded:', localProducts.length);
      } else {
        setError(response.error || 'Failed to fetch products');
        setProducts([]);
      }
    } catch (error) {
      console.error('❌ Error fetching products:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch products');
      setProducts([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStoreChange = (storeId: string) => {
    setSelectedStore(storeId);
    setCurrentPage(1);
    
    // Update URL
    const newSearchParams = new URLSearchParams();
    if (storeId !== 'all') {
      newSearchParams.set('store', storeId);
    }
    router.push(`/collections/premium?${newSearchParams.toString()}`);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleRetry = () => {
    testApiConnection();
  };

  const renderStoreSelector = () => (
    <div className="mb-8">
      <h2 className="text-lg font-semibold mb-4 text-gray-900">Select Store Collection</h2>
      <div className="flex flex-wrap gap-3">
        <button
          onClick={() => handleStoreChange('all')}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            selectedStore === 'all'
              ? 'bg-primary text-white'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          All Collections
        </button>
        {STORE_IDS.map(storeId => (
          <button
            key={storeId}
            onClick={() => handleStoreChange(storeId)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedStore === storeId
                ? 'bg-primary text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <div className="text-left">
              <div className="font-semibold">{STORE_INFO[storeId]?.name || `Collection ${storeId}`}</div>
              <div className="text-xs opacity-80">{STORE_INFO[storeId]?.description || ''}</div>
            </div>
          </button>
        ))}
      </div>
    </div>
  );

  const renderConnectionStatus = () => {
    if (connectionStatus === 'testing') {
      return (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-blue-800">Loading premium catalog...</span>
          </div>
        </div>
      );
    }
    
    if (connectionStatus === 'failed') {
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="text-red-600 mr-3">❌</div>
              <div>
                <div className="text-red-800 font-medium">Catalog Connection Failed</div>
                <div className="text-red-700 text-sm">Please try again</div>
              </div>
            </div>
            <button
              onClick={handleRetry}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    if (connectionStatus === 'success') {
      return (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <div className="text-green-600 mr-3">✅</div>
            <span className="text-green-800">Connected to Premium Catalog successfully</span>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-8">
          <motion.h1 
            className="text-4xl font-bold text-gray-900 mb-4"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Premium Hair Collections
          </motion.h1>
          <motion.p 
            className="text-lg text-gray-600 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            Discover premium hair wigs and beauty products from our curated partner stores
          </motion.p>
        </div>

        {renderConnectionStatus()}
        {renderStoreSelector()}

        {isLoading ? (
          <LoadingSkeleton count={PAGE_SIZE} />
        ) : error ? (
          <ErrorState 
            message={error} 
            onRetry={handleRetry}
          />
        ) : products.length === 0 ? (
          <EmptyState 
            title="No products found"
            message={`No products available ${selectedStore !== 'all' ? `from ${STORE_INFO[selectedStore]?.name || `Collection ${selectedStore}`}` : 'in any collection'}`}
            action={{
              label: "View All Collections",
              onClick: () => handleStoreChange('all')
            }}
          />
        ) : (
          <>
            <div className="mb-6 flex justify-between items-center">
              <div className="text-gray-600">
                <span className="font-medium">{totalProducts}</span> products found
                {selectedStore !== 'all' && (
                  <span className="ml-2 text-primary font-medium">
                    from {STORE_INFO[selectedStore]?.name || `Collection ${selectedStore}`}
                  </span>
                )}
              </div>
            </div>

            <motion.div 
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              {products.map((product, index) => (
                <motion.div
                  key={product.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.05 }}
                >
                  <ProductCard product={product} />
                </motion.div>
              ))}
            </motion.div>

            {totalProducts > PAGE_SIZE && (
              <div className="mt-12 flex justify-center">
                <div className="flex items-center space-x-2">
                  {Array.from({ length: Math.ceil(totalProducts / PAGE_SIZE) }, (_, i) => i + 1).map(page => (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                        currentPage === page
                          ? 'bg-primary text-white'
                          : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
                      }`}
                    >
                      {page}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

export default function PremiumCollections() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSkeleton count={12} />
      </div>
    }>
      <PremiumCollectionsContent />
    </Suspense>
  );
} 