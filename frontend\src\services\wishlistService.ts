import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface WishlistResponse {
  status: string;
  data: {
    wishlist: Array<{
      _id: string;
      name: string;
      price: number;
      images: string[];
      slug: string;
      rating: number;
    }>;
  };
}

class WishlistService {
  private baseURL: string;

  constructor() {
    this.baseURL = `${API_BASE_URL}/users`;
  }

  async getWishlist(): Promise<WishlistResponse['data']> {
    try {
      const response = await axios.get<WishlistResponse>(`${this.baseURL}/wishlist`);
      return response.data.data;
    } catch (error: any) {
      console.error('Error fetching wishlist:', error);
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Failed to fetch wishlist'
      );
    }
  }

  async addToWishlist(productId: string): Promise<WishlistResponse['data']> {
    try {
      const response = await axios.post<WishlistResponse>(
        `${this.baseURL}/wishlist`,
        { productId }
      );
      return response.data.data;
    } catch (error: any) {
      console.error('Error adding to wishlist:', error);
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Failed to add item to wishlist'
      );
    }
  }

  async removeFromWishlist(productId: string): Promise<WishlistResponse['data']> {
    try {
      const response = await axios.delete<WishlistResponse>(
        `${this.baseURL}/wishlist/${productId}`
      );
      return response.data.data;
    } catch (error: any) {
      console.error('Error removing from wishlist:', error);
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Failed to remove item from wishlist'
      );
    }
  }

  async isInWishlist(productId: string): Promise<boolean> {
    try {
      const wishlistData = await this.getWishlist();
      return wishlistData.wishlist.some(item => item._id === productId);
    } catch (error) {
      console.error('Error checking wishlist status:', error);
      return false;
    }
  }
}

export const wishlistService = new WishlistService();
