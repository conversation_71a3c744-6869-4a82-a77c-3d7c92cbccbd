'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface FilterHistoryItem {
  id: string;
  name: string;
  filters: any;
  timestamp: number;
  usageCount: number;
}

interface FilterHistoryProps {
  currentFilters: any;
  onApplyFilters: (filters: any) => void;
  className?: string;
}

const STORAGE_KEY = 'wig_filter_history';
const MAX_HISTORY_ITEMS = 5;

const FilterHistory: React.FC<FilterHistoryProps> = ({ 
  currentFilters, 
  onApplyFilters, 
  className = '' 
}) => {
  const [history, setHistory] = useState<FilterHistoryItem[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);

  // Load history from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        setHistory(parsed);
      }
    } catch (error) {
      console.error('Error loading filter history:', error);
    }
  }, []);

  // Save history to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(history));
    } catch (error) {
      console.error('Error saving filter history:', error);
    }
  }, [history]);

  // Generate a name for the current filter combination
  const generateFilterName = (filters: any): string => {
    const parts = [];
    
    if (filters.colors?.length > 0) {
      parts.push(`${filters.colors.length} color${filters.colors.length > 1 ? 's' : ''}`);
    }
    
    if (filters.lengths?.length > 0) {
      parts.push(`${filters.lengths.length} length${filters.lengths.length > 1 ? 's' : ''}`);
    }
    
    if (filters.styles?.length > 0) {
      parts.push(`${filters.styles.length} style${filters.styles.length > 1 ? 's' : ''}`);
    }
    
    if (filters.material?.length > 0) {
      parts.push(`${filters.material.length} material${filters.material.length > 1 ? 's' : ''}`);
    }
    
    if (filters.priceRange && (filters.priceRange[0] > 0 || filters.priceRange[1] < 1000)) {
      parts.push(`$${filters.priceRange[0]}-$${filters.priceRange[1]}`);
    }
    
    if (parts.length === 0) {
      return 'All Products';
    }
    
    return parts.join(', ');
  };

  // Check if current filters are different from default
  const hasActiveFilters = () => {
    return (
      currentFilters.colors?.length > 0 ||
      currentFilters.lengths?.length > 0 ||
      currentFilters.styles?.length > 0 ||
      currentFilters.density?.length > 0 ||
      currentFilters.capConstruction?.length > 0 ||
      currentFilters.material?.length > 0 ||
      (currentFilters.priceRange && (currentFilters.priceRange[0] > 0 || currentFilters.priceRange[1] < 1000))
    );
  };

  // Save current filters to history
  const saveCurrentFilters = () => {
    if (!hasActiveFilters()) return;

    const filterName = generateFilterName(currentFilters);
    const newItem: FilterHistoryItem = {
      id: Date.now().toString(),
      name: filterName,
      filters: { ...currentFilters },
      timestamp: Date.now(),
      usageCount: 1
    };

    setHistory(prev => {
      // Check if similar filter already exists
      const existingIndex = prev.findIndex(item => 
        JSON.stringify(item.filters) === JSON.stringify(currentFilters)
      );

      if (existingIndex >= 0) {
        // Update existing item
        const updated = [...prev];
        updated[existingIndex] = {
          ...updated[existingIndex],
          timestamp: Date.now(),
          usageCount: updated[existingIndex].usageCount + 1
        };
        return updated;
      } else {
        // Add new item and limit to MAX_HISTORY_ITEMS
        const newHistory = [newItem, ...prev].slice(0, MAX_HISTORY_ITEMS);
        return newHistory;
      }
    });
  };

  // Remove item from history
  const removeFromHistory = (id: string) => {
    setHistory(prev => prev.filter(item => item.id !== id));
  };

  // Clear all history
  const clearHistory = () => {
    setHistory([]);
  };

  // Format timestamp for display
  const formatTimestamp = (timestamp: number): string => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return new Date(timestamp).toLocaleDateString();
  };

  if (history.length === 0 && !hasActiveFilters()) {
    return null;
  }

  return (
    <div className={`bg-gray-50 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900">Filter History</h3>
        <div className="flex items-center space-x-2">
          {hasActiveFilters() && (
            <button
              onClick={saveCurrentFilters}
              className="text-xs text-primary hover:text-primary/80 font-medium"
            >
              Save Current
            </button>
          )}
          {history.length > 0 && (
            <>
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                {isExpanded ? 'Collapse' : 'Expand'}
              </button>
              <button
                onClick={clearHistory}
                className="text-xs text-red-500 hover:text-red-700"
              >
                Clear
              </button>
            </>
          )}
        </div>
      </div>

      <AnimatePresence>
        {history.length > 0 && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="space-y-2"
          >
            {history.slice(0, isExpanded ? history.length : 3).map((item, index) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                className="flex items-center justify-between p-2 bg-white rounded border border-gray-200 hover:border-gray-300 transition-colors duration-200"
              >
                <button
                  onClick={() => onApplyFilters(item.filters)}
                  className="flex-1 text-left"
                >
                  <div className="text-sm font-medium text-gray-900 truncate">
                    {item.name}
                  </div>
                  <div className="text-xs text-gray-500 flex items-center space-x-2">
                    <span>{formatTimestamp(item.timestamp)}</span>
                    {item.usageCount > 1 && (
                      <span>• Used {item.usageCount} times</span>
                    )}
                  </div>
                </button>
                
                <button
                  onClick={() => removeFromHistory(item.id)}
                  className="ml-2 text-gray-400 hover:text-red-500 transition-colors duration-200"
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </motion.div>
            ))}
            
            {!isExpanded && history.length > 3 && (
              <button
                onClick={() => setIsExpanded(true)}
                className="w-full text-center text-xs text-gray-500 hover:text-gray-700 py-1"
              >
                +{history.length - 3} more
              </button>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {history.length === 0 && hasActiveFilters() && (
        <div className="text-center py-2">
          <p className="text-sm text-gray-500 mb-2">
            Save your current filter combination for quick access later
          </p>
          <button
            onClick={saveCurrentFilters}
            className="text-sm text-primary hover:text-primary/80 font-medium"
          >
            Save Current Filters
          </button>
        </div>
      )}
    </div>
  );
};

export default FilterHistory;
