// Can be imported from a shared config
export const locales = ['en', 'es'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'en';

// Currency configuration
export const currencies = {
  en: {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    locale: 'en-US'
  },
  es: {
    code: 'EUR', // or 'MXN' for Mexican Peso, 'COP' for Colombian Peso, etc.
    symbol: '€',
    name: 'Euro',
    locale: 'es-ES'
  }
} as const;

export const currencyOptions = [
  { code: 'USD', symbol: '$', name: 'US Dollar', locale: 'en-US' },
  { code: 'EUR', symbol: '€', name: 'Euro', locale: 'es-ES' },
  { code: 'MXN', symbol: '$', name: 'Mexican Peso', locale: 'es-MX' },
  { code: 'COP', symbol: '$', name: 'Colombian Peso', locale: 'es-CO' },
  { code: 'ARS', symbol: '$', name: 'Argentine Peso', locale: 'es-AR' },
  { code: 'CLP', symbol: '$', name: 'Chilean Peso', locale: 'es-CL' }
] as const;

// Simple message loader function
export async function getMessages(locale: Locale) {
  try {
    return (await import(`./messages/${locale}.json`)).default;
  } catch (error) {
    console.warn(`Failed to load messages for locale: ${locale}`);
    return (await import(`./messages/${defaultLocale}.json`)).default;
  }
}

// Language detection configuration
export const languageDetectionConfig = {
  // Order and from where user language should be detected
  order: ['navigator', 'htmlTag', 'path', 'subdomain'],
  
  // Keys or params to lookup language from
  lookupFromPathIndex: 0,
  lookupFromSubdomainIndex: 0,
  
  // Cache user language on
  caches: ['localStorage', 'cookie'],
  
  // Optional expire and domain for set cookie
  cookieMinutes: 10080, // 7 days
  cookieDomain: typeof window !== 'undefined' ? window.location.hostname : '',
  
  // Optional htmlTag with lang attribute
  htmlTag: typeof document !== 'undefined' ? document.documentElement : null,
  
  // Only detect languages that are in the whitelist
  checkWhitelist: true
};

// Helper functions
export function getLocaleFromPathname(pathname: string): Locale {
  const segments = pathname.split('/');
  const locale = segments[1];
  
  if (locales.includes(locale as Locale)) {
    return locale as Locale;
  }
  
  return defaultLocale;
}

export function removeLocaleFromPathname(pathname: string): string {
  const segments = pathname.split('/');
  const locale = segments[1];
  
  if (locales.includes(locale as Locale)) {
    return '/' + segments.slice(2).join('/');
  }
  
  return pathname;
}

export function addLocaleToPathname(pathname: string, locale: Locale): string {
  const cleanPathname = removeLocaleFromPathname(pathname);
  return `/${locale}${cleanPathname}`;
}

// Auto-detect user's preferred language
export function detectUserLanguage(): Locale {
  if (typeof window === 'undefined') return defaultLocale;
  
  // Check localStorage first (user preference)
  const savedLocale = localStorage.getItem('preferred-locale');
  if (savedLocale && locales.includes(savedLocale as Locale)) {
    return savedLocale as Locale;
  }
  
  // Check browser language
  const browserLang = navigator.language.split('-')[0];
  if (locales.includes(browserLang as Locale)) {
    return browserLang as Locale;
  }
  
  // Check browser languages array
  for (const lang of navigator.languages) {
    const langCode = lang.split('-')[0];
    if (locales.includes(langCode as Locale)) {
      return langCode as Locale;
    }
  }
  
  return defaultLocale;
}

// Auto-detect user's preferred currency based on location/language
export function detectUserCurrency(locale: Locale): string {
  if (typeof window === 'undefined') return currencies[defaultLocale].code;
  
  // Check localStorage first (user preference)
  const savedCurrency = localStorage.getItem('preferred-currency');
  if (savedCurrency) {
    return savedCurrency;
  }
  
  // Try to detect by timezone/location
  try {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    
    // Map timezones to likely currencies
    const timezoneToCurrency: { [key: string]: string } = {
      // North America
      'America/New_York': 'USD',
      'America/Chicago': 'USD',
      'America/Denver': 'USD',
      'America/Los_Angeles': 'USD',
      'America/Toronto': 'USD',
      
      // Mexico
      'America/Mexico_City': 'MXN',
      'America/Cancun': 'MXN',
      
      // South America
      'America/Bogota': 'COP',
      'America/Buenos_Aires': 'ARS',
      'America/Santiago': 'CLP',
      'America/Lima': 'USD', // Peru often uses USD
      
      // Europe
      'Europe/Madrid': 'EUR',
      'Europe/Barcelona': 'EUR',
      'Europe/Paris': 'EUR',
      'Europe/Berlin': 'EUR',
      'Europe/Rome': 'EUR'
    };
    
    if (timezoneToCurrency[timezone]) {
      return timezoneToCurrency[timezone];
    }
  } catch (error) {
    console.warn('Could not detect timezone for currency:', error);
  }
  
  // Fallback to locale-based currency
  return currencies[locale]?.code || currencies[defaultLocale].code;
}

// Format currency based on locale and currency code
export function formatCurrency(
  amount: number, 
  currencyCode: string, 
  locale: Locale = defaultLocale
): string {
  try {
    const currency = currencyOptions.find(c => c.code === currencyCode);
    const localeString = currency?.locale || currencies[locale].locale;
    
    return new Intl.NumberFormat(localeString, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  } catch (error) {
    console.warn('Currency formatting error:', error);
    return `${currencies[locale].symbol}${amount.toFixed(2)}`;
  }
}

// Convert currency (placeholder - would integrate with real exchange rate API)
export async function convertCurrency(
  amount: number,
  fromCurrency: string,
  toCurrency: string
): Promise<number> {
  if (fromCurrency === toCurrency) return amount;
  
  try {
    // In production, integrate with a real exchange rate API
    // For now, using mock exchange rates
    const mockRates: { [key: string]: { [key: string]: number } } = {
      USD: { EUR: 0.85, MXN: 17.5, COP: 4000, ARS: 350, CLP: 800 },
      EUR: { USD: 1.18, MXN: 20.6, COP: 4700, ARS: 410, CLP: 940 },
      MXN: { USD: 0.057, EUR: 0.048, COP: 230, ARS: 20, CLP: 46 },
      COP: { USD: 0.00025, EUR: 0.00021, MXN: 0.0043, ARS: 0.087, CLP: 0.2 },
      ARS: { USD: 0.0029, EUR: 0.0024, MXN: 0.05, COP: 11.5, CLP: 2.3 },
      CLP: { USD: 0.00125, EUR: 0.00106, MXN: 0.022, COP: 5, ARS: 0.43 }
    };
    
    const rate = mockRates[fromCurrency]?.[toCurrency] || 1;
    return amount * rate;
  } catch (error) {
    console.warn('Currency conversion error:', error);
    return amount;
  }
}
