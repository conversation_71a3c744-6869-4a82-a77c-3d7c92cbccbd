import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  interval: 'monthly' | 'quarterly' | 'biannual' | 'annual';
  intervalCount: number;
  discount: number;
  features: string[];
  popular?: boolean;
}

export interface Subscription {
  id: string;
  userId: string;
  planId: string;
  plan: SubscriptionPlan;
  status: 'active' | 'paused' | 'cancelled' | 'expired';
  products: Array<{
    productId: string;
    productName: string;
    quantity: number;
    price: number;
    image: string;
  }>;
  nextDelivery: string;
  totalAmount: number;
  discountAmount: number;
  createdAt: string;
  updatedAt: string;
  deliveryAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  paymentMethod: {
    id: string;
    type: 'card' | 'paypal';
    last4?: string;
    brand?: string;
  };
}

export interface CreateSubscriptionData {
  planId: string;
  products: Array<{
    productId: string;
    quantity: number;
  }>;
  deliveryAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  paymentMethodId: string;
}

export interface UpdateSubscriptionData {
  products?: Array<{
    productId: string;
    quantity: number;
  }>;
  deliveryAddress?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  nextDelivery?: string;
}

class SubscriptionService {
  private baseURL: string;

  constructor() {
    this.baseURL = `${API_BASE_URL}/subscriptions`;
  }

  // Get available subscription plans
  async getPlans(): Promise<SubscriptionPlan[]> {
    try {
      const response = await axios.get(`${this.baseURL}/plans`);
      return response.data.data;
    } catch (error: any) {
      console.error('Error fetching subscription plans:', error);
      // Return mock data for development
      return this.getMockPlans();
    }
  }

  // Get user's subscriptions
  async getUserSubscriptions(): Promise<Subscription[]> {
    try {
      const response = await axios.get(`${this.baseURL}/user`);
      return response.data.data;
    } catch (error: any) {
      console.error('Error fetching user subscriptions:', error);
      // Return mock data for development
      return this.getMockSubscriptions();
    }
  }

  // Create a new subscription
  async createSubscription(data: CreateSubscriptionData): Promise<Subscription> {
    try {
      const response = await axios.post(`${this.baseURL}`, data);
      return response.data.data;
    } catch (error: any) {
      console.error('Error creating subscription:', error);
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Failed to create subscription'
      );
    }
  }

  // Update an existing subscription
  async updateSubscription(subscriptionId: string, data: UpdateSubscriptionData): Promise<Subscription> {
    try {
      const response = await axios.patch(`${this.baseURL}/${subscriptionId}`, data);
      return response.data.data;
    } catch (error: any) {
      console.error('Error updating subscription:', error);
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Failed to update subscription'
      );
    }
  }

  // Pause a subscription
  async pauseSubscription(subscriptionId: string): Promise<Subscription> {
    try {
      const response = await axios.patch(`${this.baseURL}/${subscriptionId}/pause`);
      return response.data.data;
    } catch (error: any) {
      console.error('Error pausing subscription:', error);
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Failed to pause subscription'
      );
    }
  }

  // Resume a subscription
  async resumeSubscription(subscriptionId: string): Promise<Subscription> {
    try {
      const response = await axios.patch(`${this.baseURL}/${subscriptionId}/resume`);
      return response.data.data;
    } catch (error: any) {
      console.error('Error resuming subscription:', error);
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Failed to resume subscription'
      );
    }
  }

  // Cancel a subscription
  async cancelSubscription(subscriptionId: string): Promise<void> {
    try {
      await axios.delete(`${this.baseURL}/${subscriptionId}`);
    } catch (error: any) {
      console.error('Error cancelling subscription:', error);
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Failed to cancel subscription'
      );
    }
  }

  // Skip next delivery
  async skipNextDelivery(subscriptionId: string): Promise<Subscription> {
    try {
      const response = await axios.patch(`${this.baseURL}/${subscriptionId}/skip`);
      return response.data.data;
    } catch (error: any) {
      console.error('Error skipping delivery:', error);
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Failed to skip delivery'
      );
    }
  }

  // Mock data for development
  private getMockPlans(): SubscriptionPlan[] {
    return [
      {
        id: 'monthly',
        name: 'Monthly Refresh',
        description: 'Perfect for trying new styles regularly',
        interval: 'monthly',
        intervalCount: 1,
        discount: 5,
        features: [
          '5% discount on all orders',
          'Free shipping',
          'Flexible delivery dates',
          'Easy product swaps',
          'Cancel anytime'
        ]
      },
      {
        id: 'quarterly',
        name: 'Quarterly Collection',
        description: 'Great balance of savings and variety',
        interval: 'quarterly',
        intervalCount: 3,
        discount: 10,
        features: [
          '10% discount on all orders',
          'Free shipping',
          'Priority customer support',
          'Exclusive early access',
          'Flexible delivery dates',
          'Cancel anytime'
        ],
        popular: true
      },
      {
        id: 'biannual',
        name: 'Bi-Annual Bundle',
        description: 'Maximum savings for loyal customers',
        interval: 'biannual',
        intervalCount: 6,
        discount: 15,
        features: [
          '15% discount on all orders',
          'Free shipping',
          'VIP customer support',
          'Exclusive products',
          'Free wig care kit',
          'Flexible delivery dates',
          'Cancel anytime'
        ]
      }
    ];
  }

  private getMockSubscriptions(): Subscription[] {
    return [
      {
        id: 'sub_1',
        userId: 'user_1',
        planId: 'quarterly',
        plan: this.getMockPlans()[1],
        status: 'active',
        products: [
          {
            productId: 'prod_1',
            productName: 'Premium Lace Front Wig - Black',
            quantity: 1,
            price: 299.99,
            image: '/images/wigs/lace-front-black.jpg'
          }
        ],
        nextDelivery: '2024-03-15',
        totalAmount: 269.99,
        discountAmount: 30.00,
        createdAt: '2024-01-15',
        updatedAt: '2024-01-15',
        deliveryAddress: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'US'
        },
        paymentMethod: {
          id: 'pm_1',
          type: 'card',
          last4: '4242',
          brand: 'visa'
        }
      }
    ];
  }
}

export const subscriptionService = new SubscriptionService();
