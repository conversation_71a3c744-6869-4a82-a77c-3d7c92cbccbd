'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { gsap } from 'gsap';
import Image from 'next/image';
import { 
  MagnifyingGlassIcon, 
  ArrowsPointingOutIcon,
  PlayIcon,
  PauseIcon,
  SpeakerWaveIcon,
  SpeakerXMarkIcon,
  XMarkIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { useSettings } from '@/contexts/SettingsContext';

interface ProductImage {
  id: string;
  url: string;
  alt: string;
  type: 'image' | '360' | 'video';
  thumbnail?: string;
  is360?: boolean;
  videoUrl?: string;
  angle?: number;
}

interface AdvancedProductGalleryProps {
  images: ProductImage[];
  productName: string;
  className?: string;
}

export default function AdvancedProductGallery({ 
  images, 
  productName, 
  className = '' 
}: AdvancedProductGalleryProps) {
  const { settings } = useSettings();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isZoomed, setIsZoomed] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [is360Mode, setIs360Mode] = useState(false);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [rotation, setRotation] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState(0);

  const mainImageRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const currentImage = images[currentImageIndex];
  const isAdvancedGalleryEnabled = settings?.features?.advanced_gallery_enabled || false;
  const is360ViewEnabled = settings?.features?.['360_view_enabled'] || false;
  const isLiveStreamingEnabled = settings?.features?.live_streaming_enabled || false;

  // Initialize GSAP animations
  useEffect(() => {
    if (mainImageRef.current) {
      gsap.set(mainImageRef.current, { scale: 1, x: 0, y: 0 });
    }
  }, [currentImageIndex]);

  // Handle zoom functionality
  const handleZoom = (event: React.MouseEvent) => {
    if (!isAdvancedGalleryEnabled || !mainImageRef.current) return;

    const rect = mainImageRef.current.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width - 0.5) * 2;
    const y = ((event.clientY - rect.top) / rect.height - 0.5) * 2;

    if (isZoomed) {
      gsap.to(mainImageRef.current, {
        x: -x * 100,
        y: -y * 100,
        duration: 0.3,
        ease: 'power2.out'
      });
    }
  };

  const toggleZoom = () => {
    if (!isAdvancedGalleryEnabled || !mainImageRef.current) return;

    setIsZoomed(!isZoomed);
    
    if (!isZoomed) {
      gsap.to(mainImageRef.current, {
        scale: 2.5,
        duration: 0.5,
        ease: 'power2.out'
      });
    } else {
      gsap.to(mainImageRef.current, {
        scale: 1,
        x: 0,
        y: 0,
        duration: 0.5,
        ease: 'power2.out'
      });
    }
  };

  // Handle 360° view
  const handle360Drag = (event: React.MouseEvent) => {
    if (!is360Mode || !isDragging) return;

    const deltaX = event.clientX - dragStart;
    const newRotation = rotation + deltaX * 0.5;
    setRotation(newRotation);
    setDragStart(event.clientX);
  };

  const start360Drag = (event: React.MouseEvent) => {
    if (!is360Mode) return;
    setIsDragging(true);
    setDragStart(event.clientX);
  };

  const end360Drag = () => {
    setIsDragging(false);
  };

  // Toggle 360° mode
  const toggle360Mode = () => {
    if (!is360ViewEnabled) return;
    setIs360Mode(!is360Mode);
    setRotation(0);
  };

  // Handle video controls
  const toggleVideo = () => {
    if (!videoRef.current) return;

    if (isVideoPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
    setIsVideoPlaying(!isVideoPlaying);
  };

  const toggleMute = () => {
    if (!videoRef.current) return;
    videoRef.current.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  // Fullscreen functionality
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Navigation
  const goToPrevious = () => {
    setCurrentImageIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const goToNext = () => {
    setCurrentImageIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (isFullscreen) {
        switch (event.key) {
          case 'ArrowLeft':
            goToPrevious();
            break;
          case 'ArrowRight':
            goToNext();
            break;
          case 'Escape':
            setIsFullscreen(false);
            break;
          case ' ':
            event.preventDefault();
            if (currentImage.type === 'video') {
              toggleVideo();
            }
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isFullscreen, currentImage.type]);

  return (
    <>
      <div className={`relative ${className}`} ref={containerRef}>
        {/* Main Image/Video Display */}
        <div className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden group">
          <div
            ref={mainImageRef}
            className="w-full h-full cursor-zoom-in"
            onClick={toggleZoom}
            onMouseMove={handleZoom}
            onMouseDown={start360Drag}
            onMouseMove={handle360Drag}
            onMouseUp={end360Drag}
            onMouseLeave={end360Drag}
            style={{
              transform: is360Mode ? `rotateY(${rotation}deg)` : undefined,
              transformStyle: 'preserve-3d'
            }}
          >
            {currentImage.type === 'video' ? (
              <video
                ref={videoRef}
                src={currentImage.videoUrl}
                className="w-full h-full object-cover"
                muted={isMuted}
                loop
                onPlay={() => setIsVideoPlaying(true)}
                onPause={() => setIsVideoPlaying(false)}
              />
            ) : (
              <Image
                src={currentImage.url}
                alt={currentImage.alt}
                fill
                className="object-cover transition-transform duration-300"
                priority={currentImageIndex === 0}
              />
            )}
          </div>

          {/* Controls Overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300">
            {/* Top Controls */}
            <div className="absolute top-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              {isAdvancedGalleryEnabled && (
                <button
                  onClick={toggleZoom}
                  className="p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all duration-200"
                  title={isZoomed ? 'Zoom Out' : 'Zoom In'}
                >
                  <MagnifyingGlassIcon className="w-5 h-5" />
                </button>
              )}
              
              <button
                onClick={toggleFullscreen}
                className="p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all duration-200"
                title="Fullscreen"
              >
                <ArrowsPointingOutIcon className="w-5 h-5" />
              </button>
            </div>

            {/* 360° Mode Toggle */}
            {is360ViewEnabled && currentImage.is360 && (
              <div className="absolute top-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <button
                  onClick={toggle360Mode}
                  className={`px-3 py-1 text-sm font-medium rounded-full transition-all duration-200 ${
                    is360Mode 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-black bg-opacity-50 text-white hover:bg-opacity-70'
                  }`}
                >
                  360°
                </button>
              </div>
            )}

            {/* Video Controls */}
            {currentImage.type === 'video' && (
              <div className="absolute bottom-4 left-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <button
                  onClick={toggleVideo}
                  className="p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all duration-200"
                >
                  {isVideoPlaying ? (
                    <PauseIcon className="w-5 h-5" />
                  ) : (
                    <PlayIcon className="w-5 h-5" />
                  )}
                </button>
                
                <button
                  onClick={toggleMute}
                  className="p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all duration-200"
                >
                  {isMuted ? (
                    <SpeakerXMarkIcon className="w-5 h-5" />
                  ) : (
                    <SpeakerWaveIcon className="w-5 h-5" />
                  )}
                </button>
              </div>
            )}

            {/* Navigation Arrows */}
            {images.length > 1 && (
              <>
                <button
                  onClick={goToPrevious}
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 p-2 bg-black bg-opacity-50 text-white rounded-full opacity-0 group-hover:opacity-100 hover:bg-opacity-70 transition-all duration-200"
                >
                  <ChevronLeftIcon className="w-5 h-5" />
                </button>
                
                <button
                  onClick={goToNext}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 bg-black bg-opacity-50 text-white rounded-full opacity-0 group-hover:opacity-100 hover:bg-opacity-70 transition-all duration-200"
                >
                  <ChevronRightIcon className="w-5 h-5" />
                </button>
              </>
            )}
          </div>
        </div>

        {/* Thumbnail Navigation */}
        {images.length > 1 && (
          <div className="flex space-x-2 mt-4 overflow-x-auto pb-2">
            {images.map((image, index) => (
              <button
                key={image.id}
                onClick={() => setCurrentImageIndex(index)}
                className={`relative flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                  index === currentImageIndex
                    ? 'border-blue-500 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <Image
                  src={image.thumbnail || image.url}
                  alt={`${productName} view ${index + 1}`}
                  fill
                  className="object-cover"
                />
                
                {/* Type Indicators */}
                {image.type === 'video' && (
                  <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
                    <PlayIcon className="w-4 h-4 text-white" />
                  </div>
                )}
                
                {image.is360 && (
                  <div className="absolute top-1 right-1 bg-blue-500 text-white text-xs px-1 rounded">
                    360°
                  </div>
                )}
              </button>
            ))}
          </div>
        )}

        {/* Feature Disabled Message */}
        {!isAdvancedGalleryEnabled && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none">
            <div className="bg-white rounded-lg p-4 text-center">
              <p className="text-sm text-gray-600">Advanced gallery features are disabled</p>
            </div>
          </div>
        )}
      </div>

      {/* Fullscreen Modal */}
      <AnimatePresence>
        {isFullscreen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black z-50 flex items-center justify-center"
          >
            {/* Close Button */}
            <button
              onClick={toggleFullscreen}
              className="absolute top-4 right-4 p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-full transition-all duration-200 z-10"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>

            {/* Fullscreen Image/Video */}
            <div className="relative max-w-full max-h-full">
              {currentImage.type === 'video' ? (
                <video
                  src={currentImage.videoUrl}
                  className="max-w-full max-h-full"
                  controls
                  autoPlay={isVideoPlaying}
                  muted={isMuted}
                />
              ) : (
                <Image
                  src={currentImage.url}
                  alt={currentImage.alt}
                  width={1200}
                  height={1200}
                  className="max-w-full max-h-full object-contain"
                />
              )}
            </div>

            {/* Fullscreen Navigation */}
            {images.length > 1 && (
              <>
                <button
                  onClick={goToPrevious}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 text-white hover:bg-white hover:bg-opacity-20 rounded-full transition-all duration-200"
                >
                  <ChevronLeftIcon className="w-8 h-8" />
                </button>
                
                <button
                  onClick={goToNext}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 text-white hover:bg-white hover:bg-opacity-20 rounded-full transition-all duration-200"
                >
                  <ChevronRightIcon className="w-8 h-8" />
                </button>
              </>
            )}

            {/* Image Counter */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
              {currentImageIndex + 1} / {images.length}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
