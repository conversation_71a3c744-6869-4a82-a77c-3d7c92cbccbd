{"name": "voice-service", "version": "1.0.0", "description": "A unified API for speech-to-text and text-to-speech functionality with multiple provider support", "main": "index.js", "scripts": {"test": "node examples/basic-usage.js", "start": "node examples/basic-usage.js"}, "dependencies": {"@google-cloud/speech": "^5.0.0", "@google-cloud/text-to-speech": "^4.0.0", "openai": "^4.0.0", "@deepgram/sdk": "^2.0.0", "aws-sdk": "^2.1000.0", "microsoft-cognitiveservices-speech-sdk": "^1.20.0", "redis": "^4.0.0", "dotenv": "^16.0.0"}, "devDependencies": {"jest": "^29.0.0"}, "engines": {"node": ">=14.0.0"}, "license": "MIT"}