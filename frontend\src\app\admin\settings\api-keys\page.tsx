'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface ApiKey {
  id: string;
  name: string;
  service: string;
  description: string;
  value: string;
  isConfigured: boolean;
  isRequired: boolean;
  lastUpdated?: string;
  status: 'active' | 'inactive' | 'error';
  category: 'payment' | 'ai' | 'communication' | 'ecommerce' | 'analytics' | 'storage';
  helpUrl?: string;
}

interface ApiKeyCategory {
  name: string;
  description: string;
  icon: string;
  keys: ApiKey[];
}

export default function ApiKeysManagementPage() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingKey, setEditingKey] = useState<string | null>(null);
  const [showValues, setShowValues] = useState<{ [key: string]: boolean }>({});
  const [testingKey, setTestingKey] = useState<string | null>(null);

  // Mock data for development
  useEffect(() => {
    const mockApiKeys: ApiKey[] = [
      // Payment Services
      {
        id: 'stripe_secret',
        name: 'Stripe Secret Key',
        service: 'Stripe',
        description: 'Secret key for processing payments via Stripe',
        value: 'sk_test_...',
        isConfigured: true,
        isRequired: true,
        lastUpdated: '2024-01-20T10:30:00Z',
        status: 'active',
        category: 'payment',
        helpUrl: 'https://stripe.com/docs/keys'
      },
      {
        id: 'stripe_public',
        name: 'Stripe Publishable Key',
        service: 'Stripe',
        description: 'Publishable key for Stripe frontend integration',
        value: 'pk_test_...',
        isConfigured: true,
        isRequired: true,
        lastUpdated: '2024-01-20T10:30:00Z',
        status: 'active',
        category: 'payment',
        helpUrl: 'https://stripe.com/docs/keys'
      },
      {
        id: 'paypal_client',
        name: 'PayPal Client ID',
        service: 'PayPal',
        description: 'Client ID for PayPal payment integration',
        value: '',
        isConfigured: false,
        isRequired: false,
        status: 'inactive',
        category: 'payment',
        helpUrl: 'https://developer.paypal.com/docs/api/overview/'
      },
      {
        id: 'paypal_secret',
        name: 'PayPal Client Secret',
        service: 'PayPal',
        description: 'Client secret for PayPal payment integration',
        value: '',
        isConfigured: false,
        isRequired: false,
        status: 'inactive',
        category: 'payment',
        helpUrl: 'https://developer.paypal.com/docs/api/overview/'
      },

      // AI Services
      {
        id: 'openai_api',
        name: 'OpenAI API Key',
        service: 'OpenAI',
        description: 'API key for ChatGPT, voice processing, and AI features',
        value: 'sk-...',
        isConfigured: true,
        isRequired: true,
        lastUpdated: '2024-01-18T15:45:00Z',
        status: 'active',
        category: 'ai',
        helpUrl: 'https://platform.openai.com/api-keys'
      },
      {
        id: 'google_ai',
        name: 'Google AI API Key',
        service: 'Google AI',
        description: 'API key for Google Vertex AI and Gemini models',
        value: '',
        isConfigured: false,
        isRequired: false,
        status: 'inactive',
        category: 'ai',
        helpUrl: 'https://cloud.google.com/vertex-ai/docs'
      },
      {
        id: 'elevenlabs_api',
        name: 'ElevenLabs API Key',
        service: 'ElevenLabs',
        description: 'API key for advanced text-to-speech services',
        value: '',
        isConfigured: false,
        isRequired: false,
        status: 'inactive',
        category: 'ai',
        helpUrl: 'https://elevenlabs.io/docs/api-reference'
      },

      // Communication Services
      {
        id: 'whatsapp_token',
        name: 'WhatsApp Access Token',
        service: 'WhatsApp Business',
        description: 'Access token for WhatsApp Business API integration',
        value: 'EAAx...',
        isConfigured: true,
        isRequired: true,
        lastUpdated: '2024-01-22T09:15:00Z',
        status: 'active',
        category: 'communication',
        helpUrl: 'https://developers.facebook.com/docs/whatsapp'
      },
      {
        id: 'whatsapp_verify',
        name: 'WhatsApp Verify Token',
        service: 'WhatsApp Business',
        description: 'Verify token for WhatsApp webhook verification',
        value: 'verify_token_123',
        isConfigured: true,
        isRequired: true,
        lastUpdated: '2024-01-22T09:15:00Z',
        status: 'active',
        category: 'communication',
        helpUrl: 'https://developers.facebook.com/docs/whatsapp'
      },
      {
        id: 'whatsapp_phone',
        name: 'WhatsApp Phone Number ID',
        service: 'WhatsApp Business',
        description: 'Phone number ID for sending WhatsApp messages',
        value: '123456789',
        isConfigured: true,
        isRequired: true,
        lastUpdated: '2024-01-22T09:15:00Z',
        status: 'active',
        category: 'communication',
        helpUrl: 'https://developers.facebook.com/docs/whatsapp'
      },

      // E-commerce Services
      {
        id: 'aliexpress_key',
        name: 'AliExpress API Key',
        service: 'AliExpress',
        description: 'API key for product synchronization from AliExpress',
        value: 'ali_...',
        isConfigured: true,
        isRequired: true,
        lastUpdated: '2024-01-15T14:20:00Z',
        status: 'active',
        category: 'ecommerce',
        helpUrl: 'https://developers.aliexpress.com/'
      },
      {
        id: 'aliexpress_secret',
        name: 'AliExpress Secret Key',
        service: 'AliExpress',
        description: 'Secret key for AliExpress API authentication',
        value: 'secret_...',
        isConfigured: true,
        isRequired: true,
        lastUpdated: '2024-01-15T14:20:00Z',
        status: 'active',
        category: 'ecommerce',
        helpUrl: 'https://developers.aliexpress.com/'
      },

      // Analytics Services
      {
        id: 'google_analytics',
        name: 'Google Analytics ID',
        service: 'Google Analytics',
        description: 'Tracking ID for Google Analytics integration',
        value: 'GA-...',
        isConfigured: true,
        isRequired: false,
        lastUpdated: '2024-01-10T11:30:00Z',
        status: 'active',
        category: 'analytics',
        helpUrl: 'https://analytics.google.com/'
      },
      {
        id: 'facebook_pixel',
        name: 'Facebook Pixel ID',
        service: 'Facebook',
        description: 'Pixel ID for Facebook advertising and analytics',
        value: '',
        isConfigured: false,
        isRequired: false,
        status: 'inactive',
        category: 'analytics',
        helpUrl: 'https://www.facebook.com/business/help/952192354843755'
      },

      // Storage Services
      {
        id: 'cloudinary_name',
        name: 'Cloudinary Cloud Name',
        service: 'Cloudinary',
        description: 'Cloud name for image and video management',
        value: 'pelucas-chic',
        isConfigured: true,
        isRequired: false,
        lastUpdated: '2024-01-12T16:45:00Z',
        status: 'active',
        category: 'storage',
        helpUrl: 'https://cloudinary.com/documentation'
      },
      {
        id: 'cloudinary_key',
        name: 'Cloudinary API Key',
        service: 'Cloudinary',
        description: 'API key for Cloudinary image management',
        value: '123456789',
        isConfigured: true,
        isRequired: false,
        lastUpdated: '2024-01-12T16:45:00Z',
        status: 'active',
        category: 'storage',
        helpUrl: 'https://cloudinary.com/documentation'
      },
      {
        id: 'cloudinary_secret',
        name: 'Cloudinary API Secret',
        service: 'Cloudinary',
        description: 'API secret for Cloudinary image management',
        value: 'secret_...',
        isConfigured: true,
        isRequired: false,
        lastUpdated: '2024-01-12T16:45:00Z',
        status: 'active',
        category: 'storage',
        helpUrl: 'https://cloudinary.com/documentation'
      }
    ];

    setTimeout(() => {
      setApiKeys(mockApiKeys);
      setIsLoading(false);
    }, 1000);
  }, []);

  const groupedKeys: ApiKeyCategory[] = [
    {
      name: 'Payment Services',
      description: 'Payment processing and financial transactions',
      icon: '💳',
      keys: apiKeys.filter(key => key.category === 'payment')
    },
    {
      name: 'AI & Machine Learning',
      description: 'Artificial intelligence and language processing',
      icon: '🤖',
      keys: apiKeys.filter(key => key.category === 'ai')
    },
    {
      name: 'Communication',
      description: 'Messaging and customer communication',
      icon: '💬',
      keys: apiKeys.filter(key => key.category === 'communication')
    },
    {
      name: 'E-commerce',
      description: 'Product sourcing and marketplace integration',
      icon: '🛒',
      keys: apiKeys.filter(key => key.category === 'ecommerce')
    },
    {
      name: 'Analytics',
      description: 'Tracking and performance monitoring',
      icon: '📊',
      keys: apiKeys.filter(key => key.category === 'analytics')
    },
    {
      name: 'Storage & Media',
      description: 'File storage and media management',
      icon: '☁️',
      keys: apiKeys.filter(key => key.category === 'storage')
    }
  ];

  const handleUpdateKey = (keyId: string, newValue: string) => {
    setApiKeys(prev => prev.map(key =>
      key.id === keyId
        ? {
            ...key,
            value: newValue,
            isConfigured: newValue.length > 0,
            status: newValue.length > 0 ? 'active' : 'inactive',
            lastUpdated: new Date().toISOString()
          }
        : key
    ));
    setEditingKey(null);
  };

  const handleTestKey = async (keyId: string) => {
    setTestingKey(keyId);
    // Simulate API test
    setTimeout(() => {
      setTestingKey(null);
      // Update status based on test result
      setApiKeys(prev => prev.map(key =>
        key.id === keyId
          ? { ...key, status: Math.random() > 0.2 ? 'active' : 'error' }
          : key
      ));
    }, 2000);
  };

  const toggleShowValue = (keyId: string) => {
    setShowValues(prev => ({ ...prev, [keyId]: !prev[keyId] }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return '✅';
      case 'inactive': return '⚪';
      case 'error': return '❌';
      default: return '⚪';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const maskValue = (value: string) => {
    if (!value) return 'Not configured';
    if (value.length <= 8) return '*'.repeat(value.length);
    return value.substring(0, 4) + '*'.repeat(Math.max(4, value.length - 8)) + value.substring(value.length - 4);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">Loading API keys...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">API Keys Management</h1>
            <p className="mt-1 text-sm text-gray-500">
              Securely manage all your external service API keys and credentials
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
              <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export Configuration
            </button>
          </div>
        </div>

        {/* Security Notice */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Security Notice</h3>
              <p className="text-sm text-yellow-700 mt-1">
                API keys are sensitive credentials. Never share them publicly or commit them to version control.
                All keys are encrypted and stored securely in environment variables.
              </p>
            </div>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <span className="text-green-600 text-sm">✅</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Configured</p>
                <p className="text-2xl font-bold text-gray-900">{apiKeys.filter(k => k.isConfigured).length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                  <span className="text-red-600 text-sm">❌</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Missing</p>
                <p className="text-2xl font-bold text-gray-900">{apiKeys.filter(k => !k.isConfigured).length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <span className="text-yellow-600 text-sm">⚠️</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Required</p>
                <p className="text-2xl font-bold text-gray-900">{apiKeys.filter(k => k.isRequired && !k.isConfigured).length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 text-sm">🔧</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Services</p>
                <p className="text-2xl font-bold text-gray-900">{groupedKeys.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* API Keys by Category */}
        {groupedKeys.map((category, categoryIndex) => (
          <motion.div
            key={category.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: categoryIndex * 0.1 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
          >
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{category.icon}</span>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{category.name}</h3>
                    <p className="text-sm text-gray-500">{category.description}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">
                    {category.keys.filter(k => k.isConfigured).length}/{category.keys.length} configured
                  </span>
                </div>
              </div>
            </div>

            <div className="divide-y divide-gray-200">
              {category.keys.map((key, keyIndex) => (
                <motion.div
                  key={key.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: (categoryIndex * 0.1) + (keyIndex * 0.05) }}
                  className="p-6 hover:bg-gray-50 transition-colors duration-200"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{getStatusIcon(key.status)}</span>
                        <div>
                          <div className="flex items-center space-x-2">
                            <h4 className="text-sm font-medium text-gray-900">{key.name}</h4>
                            {key.isRequired && (
                              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                Required
                              </span>
                            )}
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(key.status)}`}>
                              {key.status.charAt(0).toUpperCase() + key.status.slice(1)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-500 mt-1">{key.description}</p>
                          <div className="flex items-center space-x-4 mt-2">
                            <span className="text-xs text-gray-400">
                              Service: {key.service}
                            </span>
                            <span className="text-xs text-gray-400">
                              Last updated: {formatDate(key.lastUpdated)}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* API Key Value */}
                      <div className="mt-4">
                        {editingKey === key.id ? (
                          <div className="flex items-center space-x-2">
                            <input
                              type="text"
                              defaultValue={key.value}
                              placeholder="Enter API key..."
                              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleUpdateKey(key.id, e.currentTarget.value);
                                } else if (e.key === 'Escape') {
                                  setEditingKey(null);
                                }
                              }}
                              autoFocus
                            />
                            <button
                              onClick={() => {
                                const input = document.querySelector(`input[defaultValue="${key.value}"]`) as HTMLInputElement;
                                if (input) handleUpdateKey(key.id, input.value);
                              }}
                              className="px-3 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors duration-200"
                            >
                              Save
                            </button>
                            <button
                              onClick={() => setEditingKey(null)}
                              className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
                            >
                              Cancel
                            </button>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <code className="flex-1 px-3 py-2 bg-gray-100 rounded-md text-sm font-mono">
                              {showValues[key.id] ? key.value || 'Not configured' : maskValue(key.value)}
                            </code>
                            <button
                              onClick={() => toggleShowValue(key.id)}
                              className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
                              title={showValues[key.id] ? 'Hide value' : 'Show value'}
                            >
                              {showValues[key.id] ? '🙈' : '👁️'}
                            </button>
                            <button
                              onClick={() => setEditingKey(key.id)}
                              className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
                            >
                              Edit
                            </button>
                            {key.isConfigured && (
                              <button
                                onClick={() => handleTestKey(key.id)}
                                disabled={testingKey === key.id}
                                className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200 disabled:opacity-50"
                              >
                                {testingKey === key.id ? 'Testing...' : 'Test'}
                              </button>
                            )}
                            {key.helpUrl && (
                              <a
                                href={key.helpUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors duration-200"
                                title="View documentation"
                              >
                                📚
                              </a>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        ))}

        {/* Help Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">Need Help?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Getting API Keys</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Visit the service provider's developer portal</li>
                <li>• Create an account or log in</li>
                <li>• Navigate to API keys or credentials section</li>
                <li>• Generate new API keys for your application</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Security Best Practices</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Never share API keys publicly</li>
                <li>• Use environment variables for storage</li>
                <li>• Rotate keys regularly</li>
                <li>• Monitor usage and set up alerts</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}