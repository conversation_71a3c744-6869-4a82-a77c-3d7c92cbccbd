{"name": "wig-ecommerce-platform", "version": "1.0.0", "description": "Custom wig e-commerce platform with AliExpress integration", "main": "server.js", "scripts": {"start": "node backend/server.js", "dev": "cd frontend && npm run dev", "dev:server": "nodemon server.js", "test": "jest"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "morgan": "^1.10.0", "node-cron": "^3.0.3", "react-icons": "^5.5.0", "sharp": "^0.33.5", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "author": "", "license": "MIT"}