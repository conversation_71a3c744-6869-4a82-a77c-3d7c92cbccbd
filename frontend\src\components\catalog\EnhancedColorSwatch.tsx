'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface EnhancedColorSwatchProps {
  color: string;
  name: string;
  selected: boolean;
  onClick: () => void;
  size?: 'sm' | 'md' | 'lg';
  showName?: boolean;
  disabled?: boolean;
}

const EnhancedColorSwatch: React.FC<EnhancedColorSwatchProps> = ({
  color,
  name,
  selected,
  onClick,
  size = 'md',
  showName = false,
  disabled = false
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const borderSize = {
    sm: 'border-2',
    md: 'border-2',
    lg: 'border-3'
  };

  return (
    <div className="flex flex-col items-center space-y-1">
      <motion.button
        className={`
          ${sizeClasses[size]} 
          ${borderSize[size]}
          rounded-full 
          relative 
          transition-all 
          duration-200 
          focus:outline-none 
          focus:ring-2 
          focus:ring-primary 
          focus:ring-offset-2
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${selected 
            ? 'border-primary shadow-lg ring-2 ring-primary ring-offset-2' 
            : 'border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md'
          }
        `}
        style={{ backgroundColor: color }}
        onClick={!disabled ? onClick : undefined}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        whileHover={!disabled ? { scale: 1.1 } : {}}
        whileTap={!disabled ? { scale: 0.95 } : {}}
        aria-label={`Select ${name} color`}
        disabled={disabled}
      >
        {/* Selected indicator */}
        {selected && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute inset-0 flex items-center justify-center"
          >
            <svg 
              className={`${size === 'sm' ? 'w-3 h-3' : size === 'md' ? 'w-4 h-4' : 'w-6 h-6'} text-white drop-shadow-lg`}
              fill="currentColor" 
              viewBox="0 0 20 20"
            >
              <path 
                fillRule="evenodd" 
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
                clipRule="evenodd" 
              />
            </svg>
          </motion.div>
        )}

        {/* Hover effect overlay */}
        {isHovered && !selected && !disabled && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 bg-white bg-opacity-20 rounded-full"
          />
        )}

        {/* Gradient overlay for better contrast on light colors */}
        {(color === '#FFFFFF' || color === '#E6E6FA' || color === '#E5C8A8') && (
          <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 opacity-20 rounded-full" />
        )}
      </motion.button>

      {/* Color name */}
      {showName && (
        <span className={`text-xs text-gray-600 text-center leading-tight ${size === 'sm' ? 'max-w-[3rem]' : 'max-w-[4rem]'}`}>
          {name}
        </span>
      )}

      {/* Tooltip on hover */}
      {isHovered && !showName && (
        <motion.div
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute top-full mt-2 px-2 py-1 bg-gray-900 text-white text-xs rounded shadow-lg whitespace-nowrap z-10"
        >
          {name}
        </motion.div>
      )}
    </div>
  );
};

export default EnhancedColorSwatch;
