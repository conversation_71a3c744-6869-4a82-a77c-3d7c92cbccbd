# Master Environment Configuration
# Edit this ONE file and run: .\config\sync-env.ps1
# Variables will be automatically distributed to the correct services

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MONGODB_URI=mongodb+srv://riverahendrick:<EMAIL>/?retryWrites=true&w=majority&appName=pelucas-chic-cluster

# =============================================================================
# JWT & AUTHENTICATION SECRETS
# =============================================================================
JWT_SECRET=3jza5SJL2mpP7QUhr4Oe6AbolnYFKXRw
JWT_REFRESH_SECRET=MKSd9PhtbB1xp0yFCVs4He68qDiw3Xzo
JWT_RESET_SECRET=mbvE1qtwy54Zi6dcAkxXrSMeuG3fKazj
NEXTAUTH_SECRET=Qt0zELFosmkU847ZGRhKSfjOXpylDH6x
NEXTAUTH_URL=http://localhost:3000

# =============================================================================
# API ENDPOINTS
# =============================================================================
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_SITE_URL=http://localhost:3000
API_BASE_URL=http://localhost:5000/api
FRONTEND_URL=http://localhost:3000

# =============================================================================
# STRIPE PAYMENT CONFIGURATION
# =============================================================================
# Get from: https://dashboard.stripe.com/apikeys
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_SUCCESS_URL=http://localhost:3000/checkout/success
STRIPE_CANCEL_URL=http://localhost:3000/checkout/cancel

# =============================================================================
# PAYPAL PAYMENT CONFIGURATION
# =============================================================================
# Get from: https://developer.paypal.com/dashboard/
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here
NEXT_PUBLIC_PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_ENVIRONMENT=sandbox
PAYPAL_WEBHOOK_ID=your_webhook_id_here
PAYPAL_BRAND_NAME=Pelucas Chic
PAYPAL_RETURN_URL=http://localhost:3000/checkout/success
PAYPAL_CANCEL_URL=http://localhost:3000/checkout/cancel

# =============================================================================
# ALIEXPRESS INTEGRATION
# =============================================================================
# Get from: https://open.aliexpress.com/
ALIEXPRESS_APP_KEY=515696
ALIEXPRESS_APP_SECRET=Hd6k8SLMPyXZMi7LYTCqzZv4Dn4uWzrW
ALIEXPRESS_REDIRECT_URI=http://localhost:3000/api/auth/aliexpress/callback
ALIEXPRESS_STORE_IDS=3497011,4758009
ALIEXPRESS_API_URL=https://api-sg.aliexpress.com
ALIEXPRESS_RATE_LIMIT=10000
API_PROVIDER=aliexpress

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# For Gmail, use App Password: https://myaccount.google.com/apppasswords
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password_here
EMAIL_FROM=<EMAIL>

# =============================================================================
# WHATSAPP INTEGRATION
# =============================================================================
# Get from: https://developers.facebook.com/apps/
WHATSAPP_API_KEY=your_whatsapp_api_key_here
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id_here
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id_here
WHATSAPP_APP_SECRET=your_whatsapp_app_secret_here
WHATSAPP_CALLBACK_VERIFY_TOKEN=whatsapp-webhook-verification
WHATSAPP_VERIFY_TOKEN=whatsapp-webhook-verification

# =============================================================================
# INSTAGRAM INTEGRATION
# =============================================================================
# Get from: https://developers.facebook.com/
INSTAGRAM_ACCESS_TOKEN=your_instagram_access_token_here
NEXT_PUBLIC_INSTAGRAM_ACCESS_TOKEN=your_instagram_access_token_here
INSTAGRAM_USER_ID=your_instagram_user_id_here
NEXT_PUBLIC_INSTAGRAM_USER_ID=your_instagram_user_id_here
NEXT_PUBLIC_USE_MOCK_INSTAGRAM_DATA=true

# =============================================================================
# CHATBOT CONFIGURATION
# =============================================================================
NEXT_PUBLIC_CHATBOT_API_URL=http://localhost:5001/api/chatbot
NEXT_PUBLIC_CHATBOT_WS_URL=ws://localhost:5001/ws/chatbot
OPENAI_API_KEY=your_openai_api_key_here
CHATBOT_PORT=5001

# =============================================================================
# VOICE SERVICES CONFIGURATION
# =============================================================================
GOOGLE_CLOUD_API_KEY=your_google_cloud_api_key_here
GOOGLE_CLOUD_PROJECT_ID=your_project_id_here
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=eastus
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=development
PORT=5000
APP_NAME=Pelucas Chic API
LOG_LEVEL=info
LOG_FILE_PATH=logs/app.log

# =============================================================================
# REDIS CONFIGURATION (OPTIONAL)
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_CACHE_EXPIRY=3600

# =============================================================================
# INITIAL SETUP (REMOVE AFTER FIRST ADMIN CREATED)
# =============================================================================
INITIAL_SETUP_MODE=false
INITIAL_ADMIN_EMAIL=<EMAIL>
INITIAL_ADMIN_PASSWORD=your_secure_password_here 