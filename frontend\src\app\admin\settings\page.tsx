'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSettings } from '@/contexts/SettingsContext';
import AliExpressAuth from '@/components/admin/AliExpressAuth';

interface ApiKey {
  id: string;
  name: string;
  key: string;
  service: string;
  lastUsed: string;
  createdAt: string;
}

interface Setting {
  id: string;
  name: string;
  key: string;
  value: string;
  description: string;
  type: 'text' | 'boolean' | 'number' | 'select' | 'textarea' | 'password';
  options?: string[];
  category: 'general' | 'analytics' | 'seo' | 'contact' | 'social';
}

interface AdminUser {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'super-admin';
  createdAt: string;
}

export default function SettingsPage() {
  const router = useRouter();
  const { settings, getSetting, updateSetting, isLoading: settingsLoading } = useSettings();
  const [isSaving, setIsSaving] = useState(false);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [adminUsers, setAdminUsers] = useState<AdminUser[]>([]);
  const [newApiKey, setNewApiKey] = useState({ name: '', service: 'aliexpress' });
  const [newAdminUser, setNewAdminUser] = useState({ name: '', email: '', password: '', role: 'admin' });
  const [activeTab, setActiveTab] = useState('general');
  const [showApiKey, setShowApiKey] = useState('');
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' });

  // Load additional data (API keys and admin users)
  useEffect(() => {
    const loadAdditionalData = async () => {
      // Simulate API call for API keys
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setApiKeys([
        {
          id: '1',
          name: 'AliExpress API Key',
          key: 'aliex-bycm1092nw129dm10cnw01cn',
          service: 'aliexpress',
          lastUsed: '2023-05-22T10:30:00Z',
          createdAt: '2023-01-15T08:22:00Z',
        },
        {
          id: '2',
          name: 'Payment Gateway',
          key: 'live-pk-abc123def456ghi789',
          service: 'payment',
          lastUsed: '2023-05-23T14:15:00Z',
          createdAt: '2023-02-05T11:10:00Z',
        },
        {
          id: '3',
          name: 'Shipping API',
          key: 'ship-apm19294msap129',
          service: 'shipping',
          lastUsed: '2023-05-20T09:45:00Z',
          createdAt: '2023-03-10T16:30:00Z',
        },
      ]);
      
      // Simulate API call for admin users
      setAdminUsers([
        {
          id: '1',
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'super-admin',
          createdAt: '2023-01-01T10:00:00Z',
        },
      ]);
    };
    
    if (!settingsLoading) {
      loadAdditionalData();
    }
  }, [settingsLoading]);

  const handleCreateApiKey = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const newKey: ApiKey = {
      id: (apiKeys.length + 1).toString(),
      name: newApiKey.name,
      key: `${newApiKey.service}-${Math.random().toString(36).substring(2, 15)}`,
      service: newApiKey.service,
      lastUsed: 'Never',
      createdAt: new Date().toISOString(),
    };
    
    setApiKeys([...apiKeys, newKey]);
    setNewApiKey({ name: '', service: 'aliexpress' });
    setShowApiKey(newKey.key);
    showNotification('API key created successfully', 'success');
    
    setIsSaving(false);
  };

  const handleDeleteApiKey = async (id: string) => {
    if (!confirm('Are you sure you want to delete this API key? This action cannot be undone.')) {
      return;
    }
    
    setIsSaving(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 600));
    
    setApiKeys(apiKeys.filter(key => key.id !== id));
    showNotification('API key deleted successfully', 'success');
    
    setIsSaving(false);
  };

  const handleUpdateSetting = async (key: string, value: string) => {
    setIsSaving(true);
    
    const success = await updateSetting(key, value);
    
    if (success) {
      const setting = settings.find(s => s.key === key);
      showNotification(`${setting?.name || 'Setting'} updated successfully`, 'success');
    } else {
      showNotification('Failed to update setting', 'error');
    }
    
    setIsSaving(false);
  };

  const handleCreateAdminUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const newUser: AdminUser = {
      id: (adminUsers.length + 1).toString(),
      name: newAdminUser.name,
      email: newAdminUser.email,
      role: newAdminUser.role as 'admin' | 'super-admin',
      createdAt: new Date().toISOString(),
    };
    
    setAdminUsers([...adminUsers, newUser]);
    setNewAdminUser({ name: '', email: '', password: '', role: 'admin' });
    showNotification('Admin user created successfully', 'success');
    
    setIsSaving(false);
  };

  const handleDeleteAdminUser = async (id: string) => {
    if (!confirm('Are you sure you want to delete this admin user? This action cannot be undone.')) {
      return;
    }
    
    setIsSaving(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 600));
    
    setAdminUsers(adminUsers.filter(user => user.id !== id));
    showNotification('Admin user deleted successfully', 'success');
    
    setIsSaving(false);
  };

  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({ show: true, message, type });
    
    // Auto-hide notification after 5 seconds
    setTimeout(() => {
      setNotification(prev => ({ ...prev, show: false }));
    }, 5000);
  };

  // Filter settings by category
  const getSettingsByCategory = (category: string) => {
    return settings.filter(setting => setting.category === category);
  };

  // Render setting input based on type
  const renderSettingInput = (setting: any) => {
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
      e.preventDefault();
      handleUpdateSetting(setting.key, e.target.value);
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      // Prevent form submission and page navigation on Enter/Space
      if (e.key === 'Enter' && setting.type !== 'textarea') {
        e.preventDefault();
        e.stopPropagation();
      }
      // Don't prevent space in textarea fields
      if (e.key === ' ' && setting.type !== 'textarea') {
        e.stopPropagation();
      }
    };

    const commonProps = {
      value: setting.value,
      onChange: handleInputChange,
      onKeyDown: handleKeyDown,
      className: "block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm",
      disabled: isSaving,
    };

    switch (setting.type) {
      case 'boolean':
        return (
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleUpdateSetting(setting.key, setting.value === 'true' ? 'false' : 'true');
            }}
            disabled={isSaving}
            className={`relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary ${
              setting.value === 'true' ? 'bg-primary' : 'bg-gray-200'
            } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <span
              className={`pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 ${
                setting.value === 'true' ? 'translate-x-5' : 'translate-x-0'
              }`}
            />
          </button>
        );
      
      case 'select':
        return (
          <select {...commonProps}>
            {setting.options?.map((option: string) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        );
      
      case 'textarea':
        return (
          <textarea
            {...commonProps}
            rows={3}
            placeholder={`Enter ${setting.name.toLowerCase()}...`}
          />
        );
      
      case 'password':
        return (
          <input
            {...commonProps}
            type="password"
            placeholder={`Enter ${setting.name.toLowerCase()}...`}
          />
        );
      
      default:
        return (
          <input
            {...commonProps}
            type={setting.type}
            placeholder={`Enter ${setting.name.toLowerCase()}...`}
          />
        );
    }
  };

  if (settingsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Notification */}
      {notification.show && (
        <div className={`rounded-md p-4 ${
          notification.type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
        }`}>
          <div className="flex">
            <div className="flex-shrink-0">
              {notification.type === 'success' ? (
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <div className="ml-3">
              <p className={`text-sm font-medium ${
                notification.type === 'success' ? 'text-green-800' : 'text-red-800'
              }`}>
                {notification.message}
              </p>
            </div>
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  onClick={() => setNotification(prev => ({ ...prev, show: false }))}
                  className={`inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                    notification.type === 'success'
                      ? 'text-green-500 hover:bg-green-100 focus:ring-green-600' 
                      : 'text-red-500 hover:bg-red-100 focus:ring-red-600'
                  }`}
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600">Manage your store configuration and preferences</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <div className="sm:flex sm:items-baseline">
          <div className="mt-4 sm:mt-0">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              {[
                { id: 'general', name: 'General Settings', icon: '⚙️' },
                { id: 'contact', name: 'Contact Info', icon: '📞' },
                { id: 'analytics', name: 'Analytics & Tracking', icon: '📊' },
                { id: 'seo', name: 'SEO Settings', icon: '🔍' },
                { id: 'social', name: 'Social Media', icon: '📱' },
                { id: 'api-keys', name: 'API Keys', icon: '🔑' },
                { id: 'admin-users', name: 'Admin Users', icon: '👥' },
              ].map((tab) => (
              <button
                  key={tab.id}
                  onClick={(e) => {
                    e.preventDefault();
                    setActiveTab(tab.id);
                  }}
                className={`${
                    activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap pb-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
              >
                  <span>{tab.icon}</span>
                  <span>{tab.name}</span>
              </button>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {/* General Settings */}
        {activeTab === 'general' && (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-lg font-medium text-gray-800">General Settings</h4>
              <p className="text-sm text-gray-600">Basic store configuration and preferences</p>
            </div>
            
            <div className="divide-y divide-gray-200">
              {getSettingsByCategory('general').map((setting) => (
                <div key={setting.id} className="px-6 py-5 flex items-start justify-between">
                  <div className="flex-1 mr-4">
                    <h5 className="text-sm font-medium text-gray-900">{setting.name}</h5>
                    <p className="text-sm text-gray-500 mt-1">{setting.description}</p>
                  </div>
                  
                  <div className="flex-shrink-0 w-64">
                    {renderSettingInput(setting)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Contact Information */}
        {activeTab === 'contact' && (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-lg font-medium text-gray-800">Contact Information</h4>
              <p className="text-sm text-gray-600">Manage contact details displayed on your website</p>
            </div>
            
            <div className="divide-y divide-gray-200">
              {getSettingsByCategory('contact').map((setting) => (
                <div key={setting.id} className="px-6 py-5 flex items-start justify-between">
                  <div className="flex-1 mr-4">
                    <h5 className="text-sm font-medium text-gray-900">{setting.name}</h5>
                    <p className="text-sm text-gray-500 mt-1">{setting.description}</p>
                  </div>
                  
                  <div className="flex-shrink-0 w-64">
                    {renderSettingInput(setting)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Analytics & Tracking */}
        {activeTab === 'analytics' && (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-lg font-medium text-gray-800">Analytics & Tracking</h4>
              <p className="text-sm text-gray-600">Configure tracking codes for analytics and advertising platforms</p>
            </div>
            
            <div className="divide-y divide-gray-200">
              {getSettingsByCategory('analytics').map((setting) => (
                <div key={setting.id} className="px-6 py-5 flex items-start justify-between">
                  <div className="flex-1 mr-4">
                    <h5 className="text-sm font-medium text-gray-900">{setting.name}</h5>
                    <p className="text-sm text-gray-500 mt-1">{setting.description}</p>
                    {setting.key === 'custom_tracking_scripts' && (
                      <div className="mt-2 text-xs text-amber-600 bg-amber-50 p-2 rounded-md">
                        ⚠️ Only add trusted tracking scripts. Malicious code can compromise your website.
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-shrink-0 w-64">
                    {renderSettingInput(setting)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* SEO Settings */}
        {activeTab === 'seo' && (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-lg font-medium text-gray-800">SEO Settings</h4>
              <p className="text-sm text-gray-600">Search engine optimization and verification settings</p>
            </div>
            
            <div className="divide-y divide-gray-200">
              {getSettingsByCategory('seo').map((setting) => (
                <div key={setting.id} className="px-6 py-5 flex items-start justify-between">
                  <div className="flex-1 mr-4">
                    <h5 className="text-sm font-medium text-gray-900">{setting.name}</h5>
                    <p className="text-sm text-gray-500 mt-1">{setting.description}</p>
                  </div>
                  
                  <div className="flex-shrink-0 w-64">
                    {renderSettingInput(setting)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Social Media */}
        {activeTab === 'social' && (
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h4 className="text-lg font-medium text-gray-800">Social Media</h4>
              <p className="text-sm text-gray-600">Social media profile links and integration settings</p>
            </div>
            
            <div className="divide-y divide-gray-200">
              {getSettingsByCategory('social').map((setting) => (
                <div key={setting.id} className="px-6 py-5 flex items-start justify-between">
                  <div className="flex-1 mr-4">
                    <h5 className="text-sm font-medium text-gray-900">{setting.name}</h5>
                    <p className="text-sm text-gray-500 mt-1">{setting.description}</p>
                  </div>
                  
                  <div className="flex-shrink-0 w-64">
                    {renderSettingInput(setting)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* API Keys */}
        {activeTab === 'api-keys' && (
          <div className="space-y-8">
            {/* AliExpress Authorization */}
            <AliExpressAuth />
            
            {/* New API Key Form */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h4 className="text-lg font-medium text-gray-800 mb-4">Create New API Key</h4>
              
              <form onSubmit={handleCreateApiKey} className="space-y-4">
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                  <div className="sm:col-span-3">
                    <label htmlFor="api-key-name" className="block text-sm font-medium text-gray-700">
                      Name
                    </label>
                    <input
                      type="text"
                      name="api-key-name"
                      id="api-key-name"
                      value={newApiKey.name}
                      onChange={(e) => setNewApiKey({ ...newApiKey, name: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                      placeholder="e.g. AliExpress API"
                      required
                      disabled={isSaving}
                    />
                  </div>
                  
                  <div className="sm:col-span-3">
                    <label htmlFor="api-key-service" className="block text-sm font-medium text-gray-700">
                      Service
                    </label>
                    <select
                      id="api-key-service"
                      name="api-key-service"
                      value={newApiKey.service}
                      onChange={(e) => setNewApiKey({ ...newApiKey, service: e.target.value })}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                      disabled={isSaving}
                    >
                      <option value="aliexpress">AliExpress</option>
                      <option value="payment">Payment Gateway</option>
                      <option value="shipping">Shipping</option>
                      <option value="email">Email Service</option>
                      <option value="sms">SMS Provider</option>
                      <option value="analytics">Analytics</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={isSaving}
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSaving ? 'Creating...' : 'Create API Key'}
                  </button>
                </div>
              </form>
              
              {/* Show newly created key */}
              {showApiKey && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                  <h5 className="text-sm font-medium text-green-800 mb-2">New API Key Created</h5>
                  <div className="flex items-center justify-between bg-white p-2 rounded border">
                    <code className="text-sm text-gray-900 font-mono">{showApiKey}</code>
                          <button
                            onClick={() => {
                              navigator.clipboard.writeText(showApiKey);
                        showNotification('API key copied to clipboard', 'success');
                      }}
                      className="ml-2 text-green-600 hover:text-green-800"
                    >
                      Copy
                        </button>
                  </div>
                  <p className="text-xs text-green-700 mt-2">
                    ⚠️ This is the only time you'll see this key. Please save it securely.
                  </p>
                </div>
              )}
            </div>
            
            {/* API Keys List */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h4 className="text-lg font-medium text-gray-800">API Keys</h4>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Service
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Key Preview
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Last Used
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {apiKeys.map((apiKey) => (
                      <tr key={apiKey.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {apiKey.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {apiKey.service}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                            {apiKey.key.substring(0, 8)}...
                          </code>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {apiKey.lastUsed === 'Never' ? 'Never' : new Date(apiKey.lastUsed).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleDeleteApiKey(apiKey.id)}
                            disabled={isSaving}
                            className="text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
        
        {/* Admin Users */}
        {activeTab === 'admin-users' && (
          <div className="space-y-8">
            {/* New Admin User Form */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h4 className="text-lg font-medium text-gray-800 mb-4">Create New Admin User</h4>
              
              <form onSubmit={handleCreateAdminUser} className="space-y-4">
                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                  <div className="sm:col-span-3">
                    <label htmlFor="admin-name" className="block text-sm font-medium text-gray-700">
                      Full Name
                    </label>
                    <input
                      type="text"
                      name="admin-name"
                      id="admin-name"
                      value={newAdminUser.name}
                      onChange={(e) => setNewAdminUser({ ...newAdminUser, name: e.target.value })}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                      placeholder="e.g. John Doe"
                      required
                      disabled={isSaving}
                    />
                  </div>
                  
                  <div className="sm:col-span-3">
                    <label htmlFor="admin-email" className="block text-sm font-medium text-gray-700">
                      Email
                    </label>
                    <input
                      type="email"
                      name="admin-email"
                      id="admin-email"
                      value={newAdminUser.email}
                      onChange={(e) => setNewAdminUser({ ...newAdminUser, email: e.target.value })}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                      placeholder="e.g. <EMAIL>"
                      required
                      disabled={isSaving}
                    />
                  </div>

                  <div className="sm:col-span-3">
                    <label htmlFor="admin-password" className="block text-sm font-medium text-gray-700">
                      Password
                    </label>
                    <input
                      type="password"
                      name="admin-password"
                      id="admin-password"
                      value={newAdminUser.password}
                      onChange={(e) => setNewAdminUser({ ...newAdminUser, password: e.target.value })}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                      placeholder="Minimum 6 characters"
                      required
                      minLength={6}
                      disabled={isSaving}
                    />
                  </div>
                  
                  <div className="sm:col-span-3">
                    <label htmlFor="admin-role" className="block text-sm font-medium text-gray-700">
                      Role
                    </label>
                    <select
                      id="admin-role"
                      name="admin-role"
                      value={newAdminUser.role}
                      onChange={(e) => setNewAdminUser({ ...newAdminUser, role: e.target.value })}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                      disabled={isSaving}
                    >
                      <option value="admin">Admin</option>
                      <option value="super-admin">Super Admin</option>
                    </select>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={isSaving}
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSaving ? 'Creating...' : 'Create Admin User'}
                  </button>
                </div>
              </form>
            </div>
            
            {/* Admin Users List */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h4 className="text-lg font-medium text-gray-800">Admin Users</h4>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Email
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {adminUsers.map((user) => (
                      <tr key={user.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {user.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {user.email}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            user.role === 'super-admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
                          }`}>
                            {user.role}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(user.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleDeleteAdminUser(user.id)}
                            disabled={isSaving || adminUsers.length <= 1}
                            className="text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed"
                            title={adminUsers.length <= 1 ? "Cannot delete the last admin user" : "Delete user"}
                          >
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 