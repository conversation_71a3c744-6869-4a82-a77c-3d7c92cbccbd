const Product = require('../models/Product');
const Settings = require('../models/Settings');

// Get all products with filters and pagination
const getProducts = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      category,
      status,
      featured,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const filters = {};
    
    if (search) {
      filters.$text = { $search: search };
    }
    
    if (category) {
      filters.category = category;
    }
    
    if (status) {
      filters.status = status;
    }
    
    if (featured !== undefined) {
      filters.featured = featured === 'true';
    }

    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [products, total] = await Promise.all([
      Product.find(filters)
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Product.countDocuments(filters)
    ]);

    const totalPages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      data: {
        products,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: total,
          itemsPerPage: parseInt(limit),
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch products',
      error: error.message
    });
  }
};

// Get single product by ID
const getProduct = async (req, res) => {
  try {
    const { id } = req.params;
    
    const product = await Product.findById(id).lean();
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Increment view count
    await Product.findByIdAndUpdate(id, { $inc: { viewCount: 1 } });

    res.json({
      success: true,
      data: product
    });
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product',
      error: error.message
    });
  }
};

// Create new product
const createProduct = async (req, res) => {
  try {
    const productData = req.body;
    
    // Generate SKU if not provided
    if (!productData.sku) {
      productData.sku = await generateSKU(productData.name, productData.category);
    }
    
    const product = new Product(productData);
    await product.save();

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: product
    });
  } catch (error) {
    console.error('Error creating product:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Product with this SKU already exists',
        error: 'Duplicate SKU'
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to create product',
      error: error.message
    });
  }
};

// Update product
const updateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    const product = await Product.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    );
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      message: 'Product updated successfully',
      data: product
    });
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update product',
      error: error.message
    });
  }
};

// Delete product
const deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;
    
    const product = await Product.findByIdAndDelete(id);
    
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete product',
      error: error.message
    });
  }
};

// Bulk update products
const bulkUpdateProducts = async (req, res) => {
  try {
    const { productIds, updateData } = req.body;
    
    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Product IDs array is required'
      });
    }
    
    const result = await Product.updateMany(
      { _id: { $in: productIds } },
      updateData
    );

    res.json({
      success: true,
      message: `${result.modifiedCount} products updated successfully`,
      data: {
        matchedCount: result.matchedCount,
        modifiedCount: result.modifiedCount
      }
    });
  } catch (error) {
    console.error('Error bulk updating products:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to bulk update products',
      error: error.message
    });
  }
};

// Get product analytics
const getProductAnalytics = async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    let dateFilter = {};
    const now = new Date();
    
    switch (timeframe) {
      case '7d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } };
        break;
      case '30d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } };
        break;
      case '90d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) } };
        break;
    }

    const [
      totalProducts,
      activeProducts,
      featuredProducts,
      topCategories,
      topProducts,
      recentProducts,
      stockAlerts
    ] = await Promise.all([
      Product.countDocuments(),
      Product.countDocuments({ status: 'active' }),
      Product.countDocuments({ featured: true }),
      Product.aggregate([
        { $match: { status: 'active' } },
        { $group: { _id: '$category', count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 5 }
      ]),
      Product.find({ status: 'active' })
        .sort({ salesCount: -1, viewCount: -1 })
        .limit(10)
        .select('name images price salesCount viewCount')
        .lean(),
      Product.find(dateFilter)
        .sort({ createdAt: -1 })
        .limit(5)
        .select('name images price createdAt')
        .lean(),
      Product.find({ 
        status: 'active',
        stockQuantity: { $lte: 10 }
      })
        .sort({ stockQuantity: 1 })
        .select('name stockQuantity sku')
        .lean()
    ]);

    res.json({
      success: true,
      data: {
        overview: {
          totalProducts,
          activeProducts,
          featuredProducts,
          lowStockCount: stockAlerts.length
        },
        topCategories,
        topProducts,
        recentProducts,
        stockAlerts
      }
    });
  } catch (error) {
    console.error('Error fetching product analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product analytics',
      error: error.message
    });
  }
};

// Sync with AliExpress (placeholder for future implementation)
const syncWithAliExpress = async (req, res) => {
  try {
    // This would integrate with AliExpress API
    // For now, return a placeholder response
    
    const aliexpressApiKey = await Settings.getValue('api_keys', 'aliexpress_api_key');
    
    if (!aliexpressApiKey) {
      return res.status(400).json({
        success: false,
        message: 'AliExpress API key not configured'
      });
    }

    // Placeholder for AliExpress sync logic
    res.json({
      success: true,
      message: 'AliExpress sync initiated',
      data: {
        status: 'pending',
        message: 'Sync will be processed in the background'
      }
    });
  } catch (error) {
    console.error('Error syncing with AliExpress:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to sync with AliExpress',
      error: error.message
    });
  }
};

// Helper function to generate SKU
const generateSKU = async (name, category) => {
  const prefix = category.substring(0, 3).toUpperCase();
  const namePart = name.replace(/[^a-zA-Z0-9]/g, '').substring(0, 4).toUpperCase();
  const timestamp = Date.now().toString().slice(-4);
  
  let sku = `${prefix}-${namePart}-${timestamp}`;
  
  // Ensure uniqueness
  let counter = 1;
  while (await Product.findOne({ sku })) {
    sku = `${prefix}-${namePart}-${timestamp}-${counter}`;
    counter++;
  }
  
  return sku;
};

module.exports = {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  bulkUpdateProducts,
  getProductAnalytics,
  syncWithAliExpress
};
