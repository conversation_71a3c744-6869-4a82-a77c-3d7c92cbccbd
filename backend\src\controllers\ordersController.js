const Order = require('../models/Order');
const Product = require('../models/Product');
const User = require('../models/User');

// Get all orders with filters and pagination
const getOrders = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      paymentStatus,
      dateRange,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const filters = {};
    
    if (search) {
      filters.$or = [
        { orderNumber: { $regex: search, $options: 'i' } },
        { 'customer.name': { $regex: search, $options: 'i' } },
        { 'customer.email': { $regex: search, $options: 'i' } }
      ];
    }
    
    if (status) {
      filters.orderStatus = status;
    }
    
    if (paymentStatus) {
      filters['payment.status'] = paymentStatus;
    }
    
    if (dateRange) {
      const now = new Date();
      let startDate;
      
      switch (dateRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case 'quarter':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
      }
      
      if (startDate) {
        filters.createdAt = { $gte: startDate };
      }
    }

    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [orders, total] = await Promise.all([
      Order.find(filters)
        .populate('customer.userId', 'firstName lastName email')
        .populate('items.productId', 'name images sku')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit))
        .lean(),
      Order.countDocuments(filters)
    ]);

    const totalPages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalItems: total,
          itemsPerPage: parseInt(limit),
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch orders',
      error: error.message
    });
  }
};

// Get single order by ID
const getOrder = async (req, res) => {
  try {
    const { id } = req.params;
    
    const order = await Order.findById(id)
      .populate('customer.userId', 'firstName lastName email phone')
      .populate('items.productId', 'name images sku price')
      .populate('statusHistory.updatedBy', 'firstName lastName')
      .populate('communications.sentBy', 'firstName lastName')
      .lean();
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('Error fetching order:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order',
      error: error.message
    });
  }
};

// Update order status
const updateOrderStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, note } = req.body;
    
    const order = await Order.findById(id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    await order.updateStatus(status, note, req.user?.id);

    res.json({
      success: true,
      message: 'Order status updated successfully',
      data: {
        orderNumber: order.orderNumber,
        status: order.orderStatus,
        updatedAt: new Date()
      }
    });
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update order status',
      error: error.message
    });
  }
};

// Update order fulfillment
const updateOrderFulfillment = async (req, res) => {
  try {
    const { id } = req.params;
    const { trackingNumber, carrier, estimatedDelivery } = req.body;
    
    const order = await Order.findById(id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    order.fulfillment.trackingNumber = trackingNumber;
    order.fulfillment.carrier = carrier;
    order.fulfillment.estimatedDelivery = estimatedDelivery;
    
    if (trackingNumber && !order.fulfillment.shippedAt) {
      order.fulfillment.shippedAt = new Date();
      order.orderStatus = 'shipped';
    }

    await order.save();

    res.json({
      success: true,
      message: 'Order fulfillment updated successfully',
      data: order.fulfillment
    });
  } catch (error) {
    console.error('Error updating order fulfillment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update order fulfillment',
      error: error.message
    });
  }
};

// Add order communication
const addOrderCommunication = async (req, res) => {
  try {
    const { id } = req.params;
    const { type, content } = req.body;
    
    const order = await Order.findById(id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    await order.addCommunication(type, content, req.user?.id);

    res.json({
      success: true,
      message: 'Communication added successfully'
    });
  } catch (error) {
    console.error('Error adding order communication:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add communication',
      error: error.message
    });
  }
};

// Get order analytics
const getOrderAnalytics = async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    let dateFilter = {};
    const now = new Date();
    
    switch (timeframe) {
      case '7d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } };
        break;
      case '30d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } };
        break;
      case '90d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) } };
        break;
    }

    const [
      totalOrders,
      totalRevenue,
      averageOrderValue,
      ordersByStatus,
      ordersByPaymentStatus,
      recentOrders,
      topCustomers,
      salesTrend
    ] = await Promise.all([
      Order.countDocuments(dateFilter),
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $group: { _id: null, total: { $sum: '$pricing.total' } } }
      ]),
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $group: { _id: null, avg: { $avg: '$pricing.total' } } }
      ]),
      Order.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$orderStatus', count: { $sum: 1 } } }
      ]),
      Order.aggregate([
        { $match: dateFilter },
        { $group: { _id: '$payment.status', count: { $sum: 1 } } }
      ]),
      Order.find(dateFilter)
        .sort({ createdAt: -1 })
        .limit(10)
        .select('orderNumber customer pricing.total orderStatus createdAt')
        .lean(),
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $group: { 
          _id: '$customer.email', 
          totalSpent: { $sum: '$pricing.total' },
          orderCount: { $sum: 1 },
          customerName: { $first: '$customer.name' }
        }},
        { $sort: { totalSpent: -1 } },
        { $limit: 10 }
      ]),
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $group: {
          _id: { 
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          revenue: { $sum: '$pricing.total' },
          orders: { $sum: 1 }
        }},
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ])
    ]);

    res.json({
      success: true,
      data: {
        overview: {
          totalOrders,
          totalRevenue: totalRevenue[0]?.total || 0,
          averageOrderValue: averageOrderValue[0]?.avg || 0,
          conversionRate: 0 // Would need to calculate based on website visits
        },
        ordersByStatus,
        ordersByPaymentStatus,
        recentOrders,
        topCustomers,
        salesTrend
      }
    });
  } catch (error) {
    console.error('Error fetching order analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch order analytics',
      error: error.message
    });
  }
};

// Export orders to CSV
const exportOrders = async (req, res) => {
  try {
    const { 
      status,
      paymentStatus,
      dateRange,
      format = 'csv'
    } = req.query;

    const filters = {};
    
    if (status) {
      filters.orderStatus = status;
    }
    
    if (paymentStatus) {
      filters['payment.status'] = paymentStatus;
    }
    
    if (dateRange) {
      const now = new Date();
      let startDate;
      
      switch (dateRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
      }
      
      if (startDate) {
        filters.createdAt = { $gte: startDate };
      }
    }

    const orders = await Order.find(filters)
      .populate('customer.userId', 'firstName lastName email')
      .select('orderNumber customer pricing orderStatus payment.status createdAt')
      .lean();

    if (format === 'csv') {
      const csv = convertToCSV(orders);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=orders.csv');
      res.send(csv);
    } else {
      res.json({
        success: true,
        data: orders
      });
    }
  } catch (error) {
    console.error('Error exporting orders:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export orders',
      error: error.message
    });
  }
};

// Helper function to convert orders to CSV
const convertToCSV = (orders) => {
  const headers = ['Order Number', 'Customer Name', 'Customer Email', 'Total', 'Status', 'Payment Status', 'Date'];
  const rows = orders.map(order => [
    order.orderNumber,
    order.customer.name,
    order.customer.email,
    order.pricing.total,
    order.orderStatus,
    order.payment.status,
    order.createdAt.toISOString().split('T')[0]
  ]);
  
  return [headers, ...rows].map(row => row.join(',')).join('\n');
};

module.exports = {
  getOrders,
  getOrder,
  updateOrderStatus,
  updateOrderFulfillment,
  addOrderCommunication,
  getOrderAnalytics,
  exportOrders
};
