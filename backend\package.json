{"name": "pelucas-chic-api", "version": "1.0.0", "description": "Backend API for Pelucas Chic Human Hair e-commerce platform", "main": "server.js", "scripts": {"start": "node server.js", "build": "tsc", "dev": "nodemon --exec ts-node src/server.ts", "test": "jest", "test:watch": "jest --watch", "test:product-service": "jest src/services/__tests__/product.service.test.ts", "test:payment-service": "jest src/services/__tests__/payment.service.test.ts", "seed:blog": "ts-node src/scripts/seedBlog.ts", "setup:admin": "ts-node src/scripts/setupAdmin.ts", "setup:admin-simple": "ts-node src/scripts/simpleAdminSetup.ts", "test:connection": "ts-node src/scripts/testConnection.ts", "lint": "eslint ./src --ext .ts", "lint:fix": "eslint ./src --ext .ts --fix"}, "keywords": ["e-commerce", "wigs", "hair", "api"], "author": "", "license": "ISC", "dependencies": {"@types/stripe": "^8.0.416", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "compression": "^1.8.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.18.3", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.0.1", "handlebars": "^4.7.8", "helmet": "^7.1.0", "hpp": "^0.2.3", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongodb": "4.1", "mongoose": "^8.2.1", "morgan": "^1.10.0", "node-cache": "^5.1.2", "nodemailer": "^6.10.1", "openai": "^5.5.1", "pdfkit": "^0.17.1", "qrcode": "^1.5.4", "rate-limit-redis": "^4.2.0", "slugify": "^1.6.6", "stripe": "^15.12.0", "winston": "^3.11.0"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/axios": "^0.9.36", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.0", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/handlebars": "^4.0.40", "@types/helmet": "^4.0.0", "@types/hpp": "^0.2.6", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.5", "@types/long": "^4.0.2", "@types/morgan": "^1.9.9", "@types/node": "^20.11.25", "@types/node-cache": "^4.1.3", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.13.9", "@types/qrcode": "^1.5.5", "@types/rimraf": "^3.0.2", "@types/webrtc": "^0.0.46", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.27.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.4.2", "typescript-eslint": "^8.32.1"}, "engines": {"node": ">=14.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testPathIgnorePatterns": ["/node_modules/", "/dist/"], "collectCoverage": true, "coveragePathIgnorePatterns": ["/node_modules/", "/dist/"]}}