const mongoose = require('mongoose');

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  comparePrice: {
    type: Number,
    min: 0
  },
  images: [{
    url: String,
    alt: String,
    isPrimary: Boolean
  }],
  category: {
    type: String,
    required: true
  },
  subcategory: String,
  colors: [{
    name: String,
    value: String,
    image: String
  }],
  lengths: [String],
  styles: [String],
  material: {
    type: String,
    required: true
  },
  capConstruction: String,
  density: String,
  inStock: {
    type: Boolean,
    default: true
  },
  stockQuantity: {
    type: Number,
    default: 0,
    min: 0
  },
  sku: {
    type: String,
    required: true,
    unique: true
  },
  weight: Number,
  dimensions: {
    length: Number,
    width: Number,
    height: Number
  },
  seoTitle: String,
  seoDescription: String,
  tags: [String],
  featured: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['active', 'draft', 'archived'],
    default: 'draft'
  },
  aliexpressId: String,
  aliexpressUrl: String,
  variants: [{
    name: String,
    options: [String],
    prices: [{
      option: String,
      price: Number,
      comparePrice: Number
    }]
  }],
  reviews: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    verified: Boolean,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  averageRating: {
    type: Number,
    default: 0
  },
  reviewCount: {
    type: Number,
    default: 0
  },
  salesCount: {
    type: Number,
    default: 0
  },
  viewCount: {
    type: Number,
    default: 0
  },
  wishlistCount: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Indexes for better query performance
productSchema.index({ name: 'text', description: 'text', tags: 'text' });
productSchema.index({ category: 1, status: 1 });
productSchema.index({ featured: 1, status: 1 });
productSchema.index({ price: 1 });
productSchema.index({ createdAt: -1 });
productSchema.index({ sku: 1 });

// Virtual for discount percentage
productSchema.virtual('discountPercentage').get(function() {
  if (this.comparePrice && this.comparePrice > this.price) {
    return Math.round(((this.comparePrice - this.price) / this.comparePrice) * 100);
  }
  return 0;
});

// Pre-save middleware to update average rating
productSchema.pre('save', function(next) {
  if (this.reviews && this.reviews.length > 0) {
    const totalRating = this.reviews.reduce((sum, review) => sum + review.rating, 0);
    this.averageRating = totalRating / this.reviews.length;
    this.reviewCount = this.reviews.length;
  }
  next();
});

// Static method to find products with filters
productSchema.statics.findWithFilters = function(filters = {}) {
  const query = { status: 'active' };
  
  if (filters.search) {
    query.$text = { $search: filters.search };
  }
  
  if (filters.category) {
    query.category = filters.category;
  }
  
  if (filters.colors && filters.colors.length > 0) {
    query['colors.name'] = { $in: filters.colors };
  }
  
  if (filters.lengths && filters.lengths.length > 0) {
    query.lengths = { $in: filters.lengths };
  }
  
  if (filters.styles && filters.styles.length > 0) {
    query.styles = { $in: filters.styles };
  }
  
  if (filters.material && filters.material.length > 0) {
    query.material = { $in: filters.material };
  }
  
  if (filters.priceRange) {
    query.price = {
      $gte: filters.priceRange[0],
      $lte: filters.priceRange[1]
    };
  }
  
  if (filters.inStock !== undefined) {
    query.inStock = filters.inStock;
  }
  
  if (filters.featured !== undefined) {
    query.featured = filters.featured;
  }
  
  return this.find(query);
};

// Instance method to update stock
productSchema.methods.updateStock = function(quantity) {
  this.stockQuantity = Math.max(0, this.stockQuantity - quantity);
  this.inStock = this.stockQuantity > 0;
  return this.save();
};

// Instance method to add review
productSchema.methods.addReview = function(userId, rating, comment, verified = false) {
  this.reviews.push({
    userId,
    rating,
    comment,
    verified,
    createdAt: new Date()
  });
  return this.save();
};

module.exports = mongoose.model('Product', productSchema);
