import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import ProductDetail from '@/components/product/ProductDetail';
import { Product } from '@/types/product';
import { UnifiedProductService } from '@/services/unifiedProductService';

// Server-side function to get product by slug
async function getProductBySlug(slug: string): Promise<Product> {
  console.log('Server-side getProductBySlug called with slug:', slug);
  
  // Get product from unified service
  const storeProduct = await UnifiedProductService.getProductBySlug(slug);
  
  if (!storeProduct) {
    throw new Error('Product not found');
  }
  
  // Convert from StoreProduct to Product format
  const product: Product = {
    _id: storeProduct.id,
    slug: storeProduct.slug,
    name: storeProduct.title,
    description: storeProduct.description || `Premium ${storeProduct.category} from ${storeProduct.storeName}. ${storeProduct.subcategory} style with exceptional quality.`,
    price: {
      amount: storeProduct.price,
      currency: 'USD',
      originalAmount: storeProduct.originalPrice > storeProduct.price ? storeProduct.originalPrice : undefined,
      originalCurrency: 'USD',
    },
    rating: {
      average: storeProduct.rating,
      count: storeProduct.reviews
    },
    images: [
      { url: storeProduct.imageUrl, isMain: true, position: 0 },
      { url: storeProduct.imageUrl, isMain: false, position: 1 } // Using same image for now
    ],
    categories: [storeProduct.category],
    tags: [storeProduct.subcategory.toLowerCase().replace(/\s+/g, '-')],
    inventory: {
      available: Math.max(1, Math.floor(storeProduct.sales / 10)), // Estimate based on sales
      inStock: true
    },
    isActive: true,
    isFeatured: storeProduct.isFeatured,
    specifications: [
      { name: 'Store', value: storeProduct.storeName },
      { name: 'Category', value: storeProduct.category },
      { name: 'Style', value: storeProduct.subcategory },
      { name: 'Sales', value: `${storeProduct.sales} sold` },
      { name: 'Rating', value: `${storeProduct.rating} (${storeProduct.reviews} reviews)` }
    ],
    variants: [],
    sourcePlatform: 'aliexpress' as 'internal' | 'aliexpress' | 'other',
    sourceId: storeProduct.storeId,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  return product;
}

type ProductPageProps = {
  params: {
    slug: string;
  };
};

// Generate the metadata for the page dynamically
export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  // Debug slug parameter
  console.log('generateMetadata called with slug:', params.slug);
  
  try {
    const product = await getProductBySlug(params.slug);
    
    return {
      title: `${product.name} | Pelucas Chic Human Hair`,
      description: product.description || `Buy ${product.name} at Pelucas Chic Human Hair. High-quality custom wigs.`,
      openGraph: {
        title: product.name,
        description: product.description || `Buy ${product.name} at Pelucas Chic Human Hair. High-quality custom wigs.`,
        images: product.images.length > 0 ? [{ url: product.images[0].url }] : [],
      },
    };
  } catch (error) {
    console.error('Error in generateMetadata:', error);
    return {
      title: 'Product Not Found | Pelucas Chic Human Hair',
      description: 'The requested product could not be found.',
    };
  }
}

// Server Component for product detail page
export default async function ProductPage({ params }: ProductPageProps) {
  // Debug slug parameter
  console.log('ProductPage component called with slug:', params.slug);
  
  // Validate slug parameter
  if (!params.slug) {
    console.error('No slug provided in URL parameters');
    notFound();
  }

  try {
    // Fetch product data - try with normalized slug if needed
    const slug = params.slug.toLowerCase().trim();
    console.log('About to fetch product with slug:', slug);
    
    // For testing - create a direct link to a working product
    console.log('To test directly, try these URLs:');
    console.log('- http://localhost:3001/product/product-1');
    console.log('- http://localhost:3001/product/product-2');
    console.log('- http://localhost:3001/product/product-3');
    
    const product = await getProductBySlug(slug);
    
    // Validate product data
    if (!product || !product._id) {
      console.error('Product data is invalid or incomplete:', product);
      notFound();
    }
    
    console.log('Product fetched successfully:', product.slug, 'Product ID:', product._id);
    console.log('Product details:', {
      name: product.name,
      imageCount: product.images?.length || 0,
      variantCount: product.variants?.length || 0,
    });
    
    // Return the product detail page
    return (
      <main className="bg-white">
        <ProductDetail product={product} />
      </main>
    );
  } catch (error) {
    // If product not found, show 404 page
    console.error('Error fetching product:', error);
    console.log('Redirecting to 404 page');
    notFound();
  }
} 