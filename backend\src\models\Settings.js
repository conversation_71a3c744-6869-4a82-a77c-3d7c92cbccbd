const mongoose = require('mongoose');

const settingsSchema = new mongoose.Schema({
  category: {
    type: String,
    required: true,
    enum: ['api_keys', 'system', 'payment', 'whatsapp', 'voice', 'email', 'seo', 'instagram', 'chatbot', 'features']
  },
  key: {
    type: String,
    required: true
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  description: String,
  isEncrypted: {
    type: Boolean,
    default: false
  },
  isRequired: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: <PERSON><PERSON>an,
    default: true
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Compound index for category and key
settingsSchema.index({ category: 1, key: 1 }, { unique: true });

// Pre-save middleware to update lastUpdated
settingsSchema.pre('save', function(next) {
  if (this.isModified('value')) {
    this.lastUpdated = new Date();
  }
  next();
});

// Static method to get settings by category
settingsSchema.statics.getByCategory = function(category) {
  return this.find({ category, isActive: true });
};

// Static method to get setting value
settingsSchema.statics.getValue = async function(category, key, defaultValue = null) {
  const setting = await this.findOne({ category, key, isActive: true });
  return setting ? setting.value : defaultValue;
};

// Static method to set setting value
settingsSchema.statics.setValue = async function(category, key, value, updatedBy = null) {
  const setting = await this.findOneAndUpdate(
    { category, key },
    { 
      value, 
      lastUpdated: new Date(),
      updatedBy 
    },
    { 
      upsert: true, 
      new: true 
    }
  );
  return setting;
};

// Static method to initialize default settings
settingsSchema.statics.initializeDefaults = async function() {
  const defaultSettings = [
    // API Keys
    { category: 'api_keys', key: 'stripe_secret_key', value: '', description: 'Stripe Secret Key for payment processing', isEncrypted: true, isRequired: true },
    { category: 'api_keys', key: 'stripe_publishable_key', value: '', description: 'Stripe Publishable Key for frontend', isRequired: true },
    { category: 'api_keys', key: 'paypal_client_id', value: '', description: 'PayPal Client ID', isRequired: false },
    { category: 'api_keys', key: 'paypal_client_secret', value: '', description: 'PayPal Client Secret', isEncrypted: true, isRequired: false },
    { category: 'api_keys', key: 'openai_api_key', value: '', description: 'OpenAI API Key for AI features', isEncrypted: true, isRequired: true },
    { category: 'api_keys', key: 'whatsapp_access_token', value: '', description: 'WhatsApp Business API Access Token', isEncrypted: true, isRequired: true },
    { category: 'api_keys', key: 'whatsapp_verify_token', value: '', description: 'WhatsApp Webhook Verify Token', isRequired: true },
    { category: 'api_keys', key: 'whatsapp_phone_number_id', value: '', description: 'WhatsApp Phone Number ID', isRequired: true },
    { category: 'api_keys', key: 'aliexpress_api_key', value: '', description: 'AliExpress API Key', isEncrypted: true, isRequired: true },
    { category: 'api_keys', key: 'aliexpress_secret_key', value: '', description: 'AliExpress Secret Key', isEncrypted: true, isRequired: true },
    { category: 'api_keys', key: 'google_analytics_id', value: '', description: 'Google Analytics Tracking ID', isRequired: false },
    { category: 'api_keys', key: 'facebook_pixel_id', value: '', description: 'Facebook Pixel ID', isRequired: false },
    { category: 'api_keys', key: 'cloudinary_cloud_name', value: '', description: 'Cloudinary Cloud Name', isRequired: false },
    { category: 'api_keys', key: 'cloudinary_api_key', value: '', description: 'Cloudinary API Key', isRequired: false },
    { category: 'api_keys', key: 'cloudinary_api_secret', value: '', description: 'Cloudinary API Secret', isEncrypted: true, isRequired: false },
    { category: 'api_keys', key: 'elevenlabs_api_key', value: '', description: 'ElevenLabs API Key for voice synthesis', isEncrypted: true, isRequired: false },
    { category: 'api_keys', key: 'google_ai_api_key', value: '', description: 'Google AI API Key', isEncrypted: true, isRequired: false },

    // System Settings
    { category: 'system', key: 'site_name', value: 'Pelucas Chic', description: 'Website name' },
    { category: 'system', key: 'site_description', value: 'Premium Custom Wigs', description: 'Website description' },
    { category: 'system', key: 'contact_email', value: '<EMAIL>', description: 'Contact email address' },
    { category: 'system', key: 'support_phone', value: '', description: 'Support phone number' },
    { category: 'system', key: 'currency', value: 'USD', description: 'Default currency' },
    { category: 'system', key: 'timezone', value: 'America/New_York', description: 'Default timezone' },
    { category: 'system', key: 'language', value: 'en', description: 'Default language' },
    { category: 'system', key: 'maintenance_mode', value: false, description: 'Enable maintenance mode' },

    // Payment Settings
    { category: 'payment', key: 'stripe_enabled', value: true, description: 'Enable Stripe payments' },
    { category: 'payment', key: 'paypal_enabled', value: false, description: 'Enable PayPal payments' },
    { category: 'payment', key: 'apple_pay_enabled', value: false, description: 'Enable Apple Pay' },
    { category: 'payment', key: 'google_pay_enabled', value: false, description: 'Enable Google Pay' },
    { category: 'payment', key: 'tax_rate', value: 0.08, description: 'Default tax rate' },
    { category: 'payment', key: 'shipping_rate', value: 9.99, description: 'Default shipping rate' },
    { category: 'payment', key: 'free_shipping_threshold', value: 100, description: 'Free shipping threshold' },

    // WhatsApp Settings
    { category: 'whatsapp', key: 'enabled', value: true, description: 'Enable WhatsApp integration' },
    { category: 'whatsapp', key: 'auto_reply_enabled', value: true, description: 'Enable auto-reply messages' },
    { category: 'whatsapp', key: 'business_hours_start', value: '09:00', description: 'Business hours start time' },
    { category: 'whatsapp', key: 'business_hours_end', value: '18:00', description: 'Business hours end time' },
    { category: 'whatsapp', key: 'welcome_message', value: 'Welcome to Pelucas Chic! How can I help you today?', description: 'Welcome message' },

    // Voice Settings
    { category: 'voice', key: 'enabled', value: true, description: 'Enable voice processing' },
    { category: 'voice', key: 'stt_provider', value: 'openai', description: 'Speech-to-text provider' },
    { category: 'voice', key: 'tts_provider', value: 'openai', description: 'Text-to-speech provider' },
    { category: 'voice', key: 'voice_model', value: 'alloy', description: 'Default voice model' },
    { category: 'voice', key: 'language', value: 'en', description: 'Voice processing language' },

    // Email Settings
    { category: 'email', key: 'smtp_host', value: '', description: 'SMTP host' },
    { category: 'email', key: 'smtp_port', value: 587, description: 'SMTP port' },
    { category: 'email', key: 'smtp_username', value: '', description: 'SMTP username' },
    { category: 'email', key: 'smtp_password', value: '', description: 'SMTP password', isEncrypted: true },
    { category: 'email', key: 'from_email', value: '<EMAIL>', description: 'From email address' },
    { category: 'email', key: 'from_name', value: 'Pelucas Chic', description: 'From name' },

    // SEO Settings
    { category: 'seo', key: 'meta_title', value: 'Pelucas Chic - Premium Custom Wigs', description: 'Default meta title' },
    { category: 'seo', key: 'meta_description', value: 'Discover premium custom wigs crafted with care for a natural look.', description: 'Default meta description' },
    { category: 'seo', key: 'meta_keywords', value: 'custom wigs, premium wigs, hair wigs, natural wigs', description: 'Default meta keywords' },
    { category: 'seo', key: 'google_site_verification', value: '', description: 'Google site verification code' },

    // Instagram Settings
    { category: 'instagram', key: 'enabled', value: true, description: 'Enable Instagram integration' },
    { category: 'instagram', key: 'username', value: 'pelucas_chic_human_hair', description: 'Instagram username' },
    { category: 'instagram', key: 'access_token', value: '', description: 'Instagram access token', isEncrypted: true },
    { category: 'instagram', key: 'feed_limit', value: 12, description: 'Number of posts to display' },

    // Chatbot Settings
    { category: 'chatbot', key: 'enabled', value: true, description: 'Enable chatbot' },
    { category: 'chatbot', key: 'ai_provider', value: 'openai', description: 'AI provider for chatbot' },
    { category: 'chatbot', key: 'model', value: 'gpt-3.5-turbo', description: 'AI model to use' },
    { category: 'chatbot', key: 'max_tokens', value: 150, description: 'Maximum tokens per response' },
    { category: 'chatbot', key: 'temperature', value: 0.7, description: 'AI response creativity' },

    // Feature Toggles
    { category: 'features', key: 'advanced_gallery_enabled', value: false, description: 'Enable advanced product gallery features' },
    { category: 'features', key: '360_view_enabled', value: false, description: 'Enable 360° product views' },
    { category: 'features', key: 'virtual_try_on_enabled', value: false, description: 'Enable virtual try-on features' },
    { category: 'features', key: 'live_streaming_enabled', value: false, description: 'Enable live streaming capabilities' },
    { category: 'features', key: 'wishlist_enabled', value: true, description: 'Enable wishlist functionality' },
    { category: 'features', key: 'comparison_enabled', value: true, description: 'Enable product comparison' },
    { category: 'features', key: 'reviews_enabled', value: true, description: 'Enable product reviews' },
    { category: 'features', key: 'guest_checkout_enabled', value: true, description: 'Enable guest checkout' }
  ];

  for (const setting of defaultSettings) {
    await this.findOneAndUpdate(
      { category: setting.category, key: setting.key },
      setting,
      { upsert: true, new: true }
    );
  }
};

module.exports = mongoose.model('Settings', settingsSchema);
