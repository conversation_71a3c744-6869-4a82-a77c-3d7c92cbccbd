import { translationService } from './translationService';
import { Locale } from '@/i18n/config';

interface AliExpressProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: string;
  subcategory?: string;
  tags: string[];
  variants: Array<{
    name: string;
    options: string[];
    prices?: Array<{
      option: string;
      price: number;
    }>;
  }>;
  specifications: { [key: string]: string };
  shipping: {
    cost: number;
    time: string;
    countries: string[];
  };
  seller: {
    name: string;
    rating: number;
    reviews: number;
  };
  reviews: {
    count: number;
    rating: number;
    comments: Array<{
      rating: number;
      comment: string;
      date: string;
      verified: boolean;
    }>;
  };
}

interface TranslatedProduct extends AliExpressProduct {
  originalLanguage: string;
  translatedFields: string[];
  translationQuality: number;
}

class AliExpressService {
  private readonly API_BASE_URL = 'https://api.aliexpress.com/v1';
  private readonly API_KEY = process.env.ALIEXPRESS_API_KEY;
  private readonly SECRET_KEY = process.env.ALIEXPRESS_SECRET_KEY;

  constructor() {
    if (!this.API_KEY || !this.SECRET_KEY) {
      console.warn('AliExpress API credentials not configured');
    }
  }

  // Fetch product from AliExpress API
  async fetchProduct(productId: string): Promise<AliExpressProduct | null> {
    try {
      if (!this.API_KEY || !this.SECRET_KEY) {
        throw new Error('AliExpress API credentials not configured');
      }

      // This would be the actual AliExpress API call
      // For now, returning mock data
      const mockProduct: AliExpressProduct = {
        id: productId,
        name: 'Premium Human Hair Wig',
        description: 'High quality human hair wig with natural look and feel. Perfect for daily wear.',
        price: 89.99,
        originalPrice: 129.99,
        images: [
          'https://example.com/wig1.jpg',
          'https://example.com/wig2.jpg'
        ],
        category: 'Hair & Accessories',
        subcategory: 'Wigs',
        tags: ['human hair', 'natural', 'premium', 'daily wear'],
        variants: [
          {
            name: 'Color',
            options: ['Black', 'Brown', 'Blonde'],
            prices: [
              { option: 'Black', price: 89.99 },
              { option: 'Brown', price: 94.99 },
              { option: 'Blonde', price: 99.99 }
            ]
          },
          {
            name: 'Length',
            options: ['12 inch', '14 inch', '16 inch', '18 inch']
          }
        ],
        specifications: {
          'Material': '100% Human Hair',
          'Cap Construction': 'Lace Front',
          'Hair Density': '130%',
          'Hair Texture': 'Straight'
        },
        shipping: {
          cost: 0,
          time: '7-15 days',
          countries: ['US', 'CA', 'UK', 'AU', 'DE', 'FR', 'ES', 'IT']
        },
        seller: {
          name: 'Premium Hair Store',
          rating: 4.8,
          reviews: 15420
        },
        reviews: {
          count: 1250,
          rating: 4.7,
          comments: [
            {
              rating: 5,
              comment: 'Amazing quality! Looks very natural.',
              date: '2024-01-15',
              verified: true
            }
          ]
        }
      };

      return mockProduct;
    } catch (error) {
      console.error('Error fetching AliExpress product:', error);
      return null;
    }
  }

  // Fetch and translate product
  async fetchAndTranslateProduct(
    productId: string, 
    targetLanguage: Locale
  ): Promise<TranslatedProduct | null> {
    try {
      const product = await this.fetchProduct(productId);
      if (!product) return null;

      // Detect original language
      const originalLanguage = await translationService.detectLanguage(product.name);

      // If already in target language, return as-is
      if (originalLanguage === targetLanguage) {
        return {
          ...product,
          originalLanguage,
          translatedFields: [],
          translationQuality: 1.0
        };
      }

      // Translate the product
      const translatedProduct = await translationService.translateProduct(product, targetLanguage);

      // Translate specifications
      const translatedSpecs: { [key: string]: string } = {};
      for (const [key, value] of Object.entries(product.specifications)) {
        const translatedKey = await translationService.translate(key, targetLanguage, originalLanguage, 'general');
        const translatedValue = await translationService.translate(value, targetLanguage, originalLanguage, 'general');
        translatedSpecs[translatedKey] = translatedValue;
      }

      // Translate variant options
      const translatedVariants = await Promise.all(
        product.variants.map(async (variant) => ({
          ...variant,
          name: await translationService.translate(variant.name, targetLanguage, originalLanguage, 'general'),
          options: await translationService.translateBatch(variant.options, targetLanguage, originalLanguage, 'general')
        }))
      );

      // Translate reviews
      const translatedComments = await Promise.all(
        product.reviews.comments.map(async (comment) => ({
          ...comment,
          comment: await translationService.translate(comment.comment, targetLanguage, originalLanguage, 'general')
        }))
      );

      return {
        ...translatedProduct,
        specifications: translatedSpecs,
        variants: translatedVariants,
        reviews: {
          ...product.reviews,
          comments: translatedComments
        },
        originalLanguage,
        translatedFields: ['name', 'description', 'category', 'tags', 'specifications', 'variants', 'reviews'],
        translationQuality: 0.9 // Estimate based on service used
      };

    } catch (error) {
      console.error('Error fetching and translating product:', error);
      return null;
    }
  }

  // Search products with translation
  async searchProducts(
    query: string,
    targetLanguage: Locale,
    options: {
      category?: string;
      minPrice?: number;
      maxPrice?: number;
      page?: number;
      limit?: number;
      sortBy?: 'price' | 'rating' | 'sales' | 'newest';
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{
    products: TranslatedProduct[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      // This would be the actual AliExpress search API call
      // For now, returning mock data
      const mockProducts: AliExpressProduct[] = [
        {
          id: '1',
          name: 'Lace Front Human Hair Wig',
          description: 'Beautiful lace front wig made from 100% human hair',
          price: 129.99,
          originalPrice: 179.99,
          images: ['https://example.com/wig1.jpg'],
          category: 'Hair & Accessories',
          tags: ['lace front', 'human hair', 'natural'],
          variants: [],
          specifications: {},
          shipping: { cost: 0, time: '7-15 days', countries: ['US'] },
          seller: { name: 'Hair Store', rating: 4.5, reviews: 1000 },
          reviews: { count: 500, rating: 4.6, comments: [] }
        }
      ];

      // Translate all products
      const translatedProducts = await Promise.all(
        mockProducts.map(async (product) => {
          const translated = await this.fetchAndTranslateProduct(product.id, targetLanguage);
          return translated!;
        })
      );

      return {
        products: translatedProducts.filter(Boolean),
        total: translatedProducts.length,
        page: options.page || 1,
        totalPages: Math.ceil(translatedProducts.length / (options.limit || 20))
      };

    } catch (error) {
      console.error('Error searching products:', error);
      return {
        products: [],
        total: 0,
        page: 1,
        totalPages: 0
      };
    }
  }

  // Import product to local database
  async importProduct(
    aliexpressProductId: string,
    targetLanguage: Locale,
    customizations: {
      markup?: number; // Percentage markup
      category?: string; // Override category
      tags?: string[]; // Additional tags
      featured?: boolean;
    } = {}
  ): Promise<boolean> {
    try {
      const translatedProduct = await this.fetchAndTranslateProduct(aliexpressProductId, targetLanguage);
      if (!translatedProduct) return false;

      // Apply customizations
      const finalProduct = {
        ...translatedProduct,
        price: customizations.markup 
          ? translatedProduct.price * (1 + customizations.markup / 100)
          : translatedProduct.price,
        category: customizations.category || translatedProduct.category,
        tags: customizations.tags 
          ? [...translatedProduct.tags, ...customizations.tags]
          : translatedProduct.tags,
        featured: customizations.featured || false,
        aliexpressId: aliexpressProductId,
        importedAt: new Date().toISOString()
      };

      // Save to local database
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(finalProduct)
      });

      return response.ok;

    } catch (error) {
      console.error('Error importing product:', error);
      return false;
    }
  }

  // Sync product updates from AliExpress
  async syncProduct(localProductId: string, aliexpressProductId: string): Promise<boolean> {
    try {
      // Fetch current local product
      const localResponse = await fetch(`/api/admin/products/${localProductId}`);
      if (!localResponse.ok) return false;

      const localProduct = await localResponse.json();
      const targetLanguage = localProduct.language || 'en';

      // Fetch updated product from AliExpress
      const updatedProduct = await this.fetchAndTranslateProduct(aliexpressProductId, targetLanguage);
      if (!updatedProduct) return false;

      // Update only specific fields (price, availability, etc.)
      const updateData = {
        price: updatedProduct.price,
        originalPrice: updatedProduct.originalPrice,
        images: updatedProduct.images,
        variants: updatedProduct.variants,
        specifications: updatedProduct.specifications,
        lastSyncedAt: new Date().toISOString()
      };

      // Update local product
      const updateResponse = await fetch(`/api/admin/products/${localProductId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(updateData)
      });

      return updateResponse.ok;

    } catch (error) {
      console.error('Error syncing product:', error);
      return false;
    }
  }

  // Get product categories with translation
  async getCategories(targetLanguage: Locale): Promise<Array<{
    id: string;
    name: string;
    subcategories: Array<{ id: string; name: string; }>;
  }>> {
    try {
      // Mock categories - in real implementation, fetch from AliExpress
      const categories = [
        {
          id: 'hair-accessories',
          name: 'Hair & Accessories',
          subcategories: [
            { id: 'wigs', name: 'Wigs' },
            { id: 'hair-extensions', name: 'Hair Extensions' },
            { id: 'hair-tools', name: 'Hair Tools' }
          ]
        }
      ];

      // Translate categories
      const translatedCategories = await Promise.all(
        categories.map(async (category) => ({
          ...category,
          name: await translationService.translate(category.name, targetLanguage, 'en', 'category'),
          subcategories: await Promise.all(
            category.subcategories.map(async (sub) => ({
              ...sub,
              name: await translationService.translate(sub.name, targetLanguage, 'en', 'category')
            }))
          )
        }))
      );

      return translatedCategories;

    } catch (error) {
      console.error('Error fetching categories:', error);
      return [];
    }
  }
}

// Create singleton instance
export const aliexpressService = new AliExpressService();

// Hook for using AliExpress service in React components
export function useAliExpress() {
  return {
    fetchProduct: aliexpressService.fetchProduct.bind(aliexpressService),
    fetchAndTranslateProduct: aliexpressService.fetchAndTranslateProduct.bind(aliexpressService),
    searchProducts: aliexpressService.searchProducts.bind(aliexpressService),
    importProduct: aliexpressService.importProduct.bind(aliexpressService),
    syncProduct: aliexpressService.syncProduct.bind(aliexpressService),
    getCategories: aliexpressService.getCategories.bind(aliexpressService)
  };
}
