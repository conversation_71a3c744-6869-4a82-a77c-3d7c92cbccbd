import axios from 'axios';
import crypto from 'crypto';
import querystring from 'querystring';
import logger from '../../config/logger.config';
import config from '../../config/env.config';
import AppError from '../../utils/errors/AppError';
import StoreToken from '../../models/store-token.model';
import Order from '../../models/order.model';

// Define types for AliExpress API responses
interface AliExpressTokenResponse {
  token?: string;
  [key: string]: any;
}

interface AliExpressErrorResponse {
  error_response?: {
    code?: string;
    msg?: string;
    sub_code?: string;
    sub_msg?: string;
  };
  code?: string;
  msg?: string;
  sub_code?: string;
  sub_msg?: string;
  [key: string]: any;
}

// Order-specific types
interface AliExpressOrderResponse {
  order_id?: string;
  external_order_id?: string;
  status?: string;
  tracking_number?: string;
  tracking_url?: string;
  order_details?: {
    items: Array<{
      product_id: string;
      sku?: string;
      quantity: number;
      price: {
        amount: number;
        currency_code: string;
      };
    }>;
    shipping_info?: {
      address: {
        name: string;
        street1: string;
        street2?: string;
        city: string;
        state?: string;
        country_code: string;
        zipcode: string;
        phone?: string;
        email?: string;
      };
      shipping_method?: string;
      shipping_carrier?: string;
      estimated_delivery?: string;
    };
    payment_info?: {
      amount: number;
      currency_code: string;
      payment_method: string;
      payment_status: string;
    };
  };
  created_at?: string;
  updated_at?: string;
  [key: string]: any;
}

interface AliExpressOrderStatusSyncResponse {
  order_id: string;
  status: string;
  tracking_number?: string;
  tracking_url?: string;
  updated_at?: string;
  [key: string]: any;
}

interface AliExpressOrderWebhookPayload {
  event_type: string;
  order_id: string;
  status?: string;
  tracking_number?: string;
  tracking_url?: string;
  updated_at?: string;
  [key: string]: any;
}

/**
 * AliExpress Order Service
 * Handles order operations with the AliExpress API
 */
class AliExpressOrderService {
  /**
   * Generate signature for AliExpress API authentication
   * @param params Request parameters
   * @param secret App secret
   * @returns Signature string
   */
  private generateSignature(params: Record<string, any>, secret: string): string {
    // Sort parameters alphabetically by key
    const sortedKeys = Object.keys(params).sort();
    
    // Concatenate key-value pairs
    let signatureString = '';
    sortedKeys.forEach(key => {
      const value = params[key];
      if (value !== undefined && value !== null) {
        signatureString += `${key}${value}`;
      }
    });
    
    // Prepend app secret
    signatureString = secret + signatureString;
    
    // Generate MD5 hash
    return crypto.createHash('md5').update(signatureString).digest('hex');
  }
  
  /**
   * Get access token from StoreToken model or refresh if needed
   * @param storeId AliExpress store ID to get token for
   * @returns Valid access token
   */
  private async getAccessToken(storeId?: string): Promise<string> {
    try {
      // If no storeId is provided, use the first available storeId from config
      const targetStoreId = storeId || (config.aliexpress?.stores?.ids?.length > 0 ? config.aliexpress.stores.ids[0] : undefined);
      
      if (!targetStoreId) {
        throw new Error('No store ID provided or configured');
      }
      
      // Try to find an existing token for this store
      const storeToken = await StoreToken.findOne({ 
        storeId: targetStoreId, 
        provider: 'aliexpress',
        isActive: true 
      });
      
      if (!storeToken) {
        logger.warn(`No token found for AliExpress store ${targetStoreId}. OAuth authorization needed.`);
        throw new AppError(`No OAuth token available for AliExpress store ${targetStoreId}. Store owner must authorize via OAuth.`, 401);
      }
      
      // Check if token is expired and needs refresh
      if (storeToken.isExpired()) {
        logger.info(`Refreshing expired token for AliExpress store ${targetStoreId}`);
        
        const { appKey, appSecret, apiUrl } = config.aliexpress || {};
        if (!appKey || !appSecret) {
          throw new Error('Missing AliExpress API credentials');
        }
        
        // Prepare parameters for token refresh request
        const refreshParams = {
          app_key: appKey,
          refresh_token: storeToken.refreshToken,
          grant_type: 'refresh_token',
          timestamp: new Date().toISOString(),
          sign_method: 'md5'
        };
        
        // Generate signature
        const signature = this.generateSignature(refreshParams, appSecret);
        
        // Make token refresh request
        const response = await axios.post<AliExpressTokenResponse>(
          `${apiUrl || 'https://api.aliexpress.com'}/auth/token/refresh`,
          null,
          {
            params: {
              ...refreshParams,
              sign: signature
            }
          }
        );
        
        // Check for errors in response
        if (!response.data.token) {
          logger.error('Failed to refresh AliExpress API token');
          throw new Error('Failed to refresh AliExpress API token');
        }
        
        // Update token in database
        storeToken.accessToken = response.data.token;
        storeToken.expiresAt = new Date(Date.now() + 3600 * 1000); // Default 1hr expiry
        if (response.data.refresh_token) {
          storeToken.refreshToken = response.data.refresh_token;
        }
        await storeToken.save();
        
        return storeToken.accessToken;
      }
      
      // Return valid token
      return storeToken.accessToken;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error getting AliExpress API token: ${errorMessage}`);
      throw new AppError(`Failed to obtain AliExpress API token: ${errorMessage}`, 500);
    }
  }
  
  /**
   * Make a call to the AliExpress API
   * @param endpoint API endpoint
   * @param params Request parameters
   * @param method HTTP method
   * @param storeId Optional store ID
   * @returns API response
   */
  private async callAliExpressAPI<T>(endpoint: string, params: Record<string, any> = {}, method: 'GET' | 'POST' = 'GET', storeId?: string): Promise<T> {
    try {
      const { appKey, appSecret, apiUrl } = config.aliexpress || {};
      
      if (!appKey || !appSecret) {
        throw new Error('Missing AliExpress API credentials in config');
      }
      
      // Get access token for the specified store
      const accessToken = await this.getAccessToken(storeId);
      
      // Prepare request parameters
      const requestParams = {
        ...params,
        app_key: appKey,
        session: accessToken,
        timestamp: new Date().toISOString(),
        sign_method: 'md5',
      };
      
      // Generate signature
      const signature = this.generateSignature(requestParams, appSecret);
      
      // Add signature to params
      const finalParams = {
        ...requestParams,
        sign: signature
      };
      
      // Make API request
      let response: any;
      const url = `${apiUrl || 'https://api.aliexpress.com'}/${endpoint}`;
      
      if (method === 'GET') {
        response = await axios.get<AliExpressErrorResponse>(url, { params: finalParams });
      } else if (method === 'POST') {
        response = await axios.post<AliExpressErrorResponse>(url, querystring.stringify(finalParams));
      } else {
        throw new Error(`Unsupported method: ${method}`);
      }
      
      // Check for API errors in response
      const responseData = response.data as AliExpressErrorResponse;
      if (responseData && responseData.error_response) {
        const apiError = responseData.error_response;
        logger.error(`AliExpress API error: ${apiError.msg || 'Unknown API error'}`);
        throw new Error(`AliExpress API error: ${apiError.msg || 'Unknown API error'}`);
      }
      
      return responseData as T;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error calling AliExpress API (${endpoint}): ${errorMessage}`);
      throw new AppError(`AliExpress API error (${endpoint}): ${errorMessage}`, 500);
    }
  }
  
  /**
   * Format address for AliExpress API
   * @param address Address object from our system
   * @returns Address formatted for AliExpress API
   */
  private formatAddressForApi(address: any): Record<string, string> {
    return {
      contact_person: address.name || '',
      address_line1: address.street1 || address.address1 || '',
      address_line2: address.street2 || address.address2 || '',
      city: address.city || '',
      province: address.state || '',
      postal_code: address.zipcode || address.postalCode || '',
      country_code: address.country_code || address.countryCode || '',
      phone_number: address.phone || '',
      email: address.email || '',
    };
  }
  
  /**
   * Format order items for AliExpress API
   * @param items Array of order items
   * @returns Items formatted for AliExpress API
   */
  private formatOrderItemsForApi(items: any[]): Array<{
    product_id: string;
    sku_id?: string;
    quantity: number;
    price?: number;
  }> {
    return items.map(item => ({
      product_id: item.sourceId || item.productId,
      sku_id: item.variantId || item.skuId || undefined,
      quantity: item.quantity || 1,
      price: item.price?.amount
    }));
  }

  /**
   * Place order with AliExpress
   * @param order Order data from our system
   * @param aliExpressData Additional data for AliExpress API
   * @returns AliExpress order details
   */
  public async placeOrder(order: any, aliExpressData: any): Promise<{
    orderId: string;
    orderStatus: string;
    trackingNumber: string;
    orderUrl: string;
    lastSyncDate: Date;
  }> {
    try {
      logger.info(`Placing AliExpress order for order ID: ${order._id}`);
      
      // Format data for API
      const apiOrderData = {
        external_order_id: order._id.toString(),
        shipping_address: this.formatAddressForApi(order.shippingAddress),
        items: this.formatOrderItemsForApi(order.items),
        shipping_method: aliExpressData?.shippingMethod || 'standard',
        // Add any additional parameters required by the API
        ...aliExpressData
      };
      
      // Call AliExpress API to place order
      const response = await this.callAliExpressAPI<AliExpressOrderResponse>(
        'trade/placeOrder',
        apiOrderData,
        'POST',
        aliExpressData?.storeId
      );
      
      // Validate response
      if (!response.order_id) {
        throw new Error('Failed to place AliExpress order - missing order ID in response');
      }
      
      // Extract and return order details
      return {
        orderId: response.order_id,
        orderStatus: response.status || 'PLACE_ORDER_SUCCESS',
        trackingNumber: response.tracking_number || '',
        orderUrl: `https://www.aliexpress.com/orders/${response.order_id}`,
        lastSyncDate: new Date()
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error placing AliExpress order: ${errorMessage}`);
      throw new AppError(`Failed to place AliExpress order: ${errorMessage}`, 500);
    }
  }
  
  /**
   * Sync order status from AliExpress
   * @param order Order data with AliExpress orderId
   * @returns Updated order status and tracking details
   */
  public async syncOrderStatus(order: any): Promise<{
    orderId: string;
    orderStatus: string;
    trackingNumber: string;
    trackingUrl: string;
    lastSyncDate: Date;
  }> {
    try {
      if (!order.aliExpressOrder?.orderId) {
        throw new Error('No AliExpress order ID available for syncing');
      }
      
      logger.info(`Syncing AliExpress order status for order ID: ${order._id}, AliExpress order ID: ${order.aliExpressOrder.orderId}`);
      
      // Call AliExpress API to get order status
      const response = await this.callAliExpressAPI<AliExpressOrderStatusSyncResponse>(
        'trade/getOrderStatus',
        {
          order_id: order.aliExpressOrder.orderId
        },
        'GET',
        order.aliExpressOrder?.storeId
      );
      
      // Extract and return updated order details
      return {
        orderId: order.aliExpressOrder.orderId,
        orderStatus: response.status || order.aliExpressOrder.orderStatus || 'Processing',
        trackingNumber: response.tracking_number || order.aliExpressOrder.trackingNumber || '',
        trackingUrl: response.tracking_url || order.aliExpressOrder.trackingUrl || '',
        lastSyncedAt: new Date()
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error syncing AliExpress order status: ${errorMessage}`);
      throw new AppError(`Failed to sync AliExpress order status: ${errorMessage}`, 500);
    }
  }
  
  /**
   * Process payment for an AliExpress order
   * @param orderId MongoDB order ID
   * @returns Payment result
   */
  public async payForOrder(orderId: string): Promise<{
    success: boolean;
    paymentId?: string;
    message: string;
  }> {
    try {
      // 1. Get order details
      const order = await Order.findById(orderId);
      if (!order) {
        throw new AppError('Order not found', 404);
      }

      if (!order.aliExpressOrder?.orderId) {
        throw new AppError('No AliExpress order ID found', 400);
      }

      // 2. Update payment attempts
      order.aliExpressOrder.paymentAttempts = 
        (order.aliExpressOrder.paymentAttempts || 0) + 1;
      order.aliExpressOrder.lastPaymentAttempt = new Date();
      order.aliExpressOrder.paymentToSupplierStatus = 'processing';
      await order.save();

      // 3. Calculate cost of goods if not set
      if (!order.aliExpressOrder.costOfGoods && order.orderItems?.length > 0) {
        // Since we don't have direct access to supplier costs in this example,
        // we'll estimate it as 60% of the retail price (this would be replaced with actual API data)
        // In production, this should come from the actual AliExpress API response during order placement
        const costEstimate = order.orderItems.reduce((total, item) => {
          return total + (item.price * item.quantity * 0.6); // 60% of retail as example
        }, 0);
        order.aliExpressOrder.costOfGoods = costEstimate;
        await order.save();
      }

      // 4. Call AliExpress payment API
      // In a real implementation, you would call the payment endpoint:
      // 'aliexpress.trade.order.pay' or similar
      const response = await this.callAliExpressAPI<{
        payment_id: string;
        status: string;
        message: string;
      }>('aliexpress.trade.ds.order.pay', {
        order_id: order.aliExpressOrder.orderId,
        store_id: order.aliExpressOrder.storeId || '',
        // This would need to be replaced with actual payment method configuration
        // from your secure settings storage
        payment_method: 'stored_payment_method', 
      });

      // 5. Update order with payment result
      order.aliExpressOrder.paymentToSupplierStatus = 
        response.status === 'success' ? 'completed' : 'failed';
      
      if (response.status === 'success') {
        order.aliExpressOrder.paymentToSupplierDate = new Date();
        
        // Add a status history entry
        order.statusHistory.push({
          status: 'AliExpress Payment Complete',
          note: `Payment sent to supplier. Payment ID: ${response.payment_id}`,
          timestamp: new Date(),
          updatedBy: 'system'
        });
      } else {
        // Add a status history entry for failed payment
        order.statusHistory.push({
          status: 'AliExpress Payment Failed',
          note: `Payment failed: ${response.message}`,
          timestamp: new Date(),
          updatedBy: 'system'
        });
      }
      
      await order.save();

      return {
        success: response.status === 'success',
        paymentId: response.payment_id,
        message: response.message || 'Payment processed'
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error paying for AliExpress order: ${errorMessage}`);
      
      // Try to update the order status if we have the order ID
      try {
        if (typeof orderId === 'string') {
          const order = await Order.findById(orderId);
          if (order?.aliExpressOrder) {
            order.aliExpressOrder.paymentToSupplierStatus = 'failed';
            order.statusHistory.push({
              status: 'AliExpress Payment Error',
              note: `Payment processing error: ${errorMessage}`,
              timestamp: new Date(),
              updatedBy: 'system'
            });
            await order.save();
          }
        }
      } catch (saveError) {
        logger.error(`Failed to update order after payment error: ${saveError}`);
      }
      
      throw new AppError(`Failed to pay for AliExpress order: ${errorMessage}`, 500);
    }
  }
  
  /**
   * Process webhook payload from AliExpress
   * @param payload Webhook payload from AliExpress
   * @returns Process result
   */
  public async handleWebhook(payload: AliExpressOrderWebhookPayload): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      logger.info(`Processing AliExpress webhook for order ID: ${payload.order_id}`);
      
      // Validate required fields
      if (!payload.order_id || !payload.event_type) {
        throw new Error('Invalid webhook payload - missing required fields');
      }
      
      // Find order with this AliExpress ID
      const order = await Order.findOne({ 'aliexpress.orderId': payload.order_id });
      
      if (!order) {
        logger.warn(`AliExpress webhook received for unknown order ID: ${payload.order_id}`);
        return { success: false, message: 'Order not found' };
      }
      
      // Update order based on event type
      switch (payload.event_type) {
        case 'order_status_update':
          // Update order status
          order.aliExpressOrder = {
            ...order.aliExpressOrder,
            orderStatus: payload.status || order.aliExpressOrder.orderStatus,
            trackingNumber: payload.tracking_number || order.aliExpressOrder.trackingNumber || '',
            trackingUrl: payload.tracking_url || order.aliExpressOrder.trackingUrl || '',
            lastSyncDate: new Date()
          };
          
          // Update main order status based on AliExpress status
          if (payload.status) {
            // Map AliExpress status to our status
            const statusMap: Record<string, string> = {
              'PLACE_ORDER_SUCCESS': 'Processing',
              'IN_CANCEL': 'Cancelled',
              'WAIT_BUYER_ACCEPT_GOODS': 'Shipped',
              'FINISH': 'Delivered'
            };
            
            const newStatus = statusMap[payload.status] || order.status;
            if (newStatus !== order.status) {
              order.status = newStatus;
              order.statusHistory.push({
                status: newStatus,
                timestamp: new Date(),
                note: `Status updated from AliExpress webhook: ${payload.status}`
              });
            }
          }
          
          await order.save();
          return { success: true, message: `Order ${order._id} status updated to ${order.status}` };
          
        case 'tracking_update':
          // Update tracking info
          order.aliExpressOrder = {
            ...order.aliExpressOrder,
            trackingNumber: payload.tracking_number || order.aliExpressOrder.trackingNumber || '',
            trackingUrl: payload.tracking_url || order.aliExpressOrder.trackingUrl || '',
            lastSyncDate: new Date()
          };
          
          if (order.status === 'Processing' && payload.tracking_number) {
            order.status = 'Shipped';
            order.statusHistory.push({
              status: 'Shipped',
              timestamp: new Date(),
              note: `Tracking information received from AliExpress: ${payload.tracking_number}`
            });
          }
          
          await order.save();
          return { success: true, message: `Order ${order._id} tracking updated: ${payload.tracking_number}` };
          
        default:
          logger.info(`Unhandled AliExpress webhook event type: ${payload.event_type}`);
          return { success: true, message: `Event type ${payload.event_type} acknowledged but not processed` };
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      logger.error(`Error processing AliExpress webhook: ${errorMessage}`);
      throw new AppError(`Failed to process AliExpress webhook: ${errorMessage}`, 500);
    }
  }
}

// Create and export an instance of the service
const aliExpressOrderService = new AliExpressOrderService();
export default aliExpressOrderService;
