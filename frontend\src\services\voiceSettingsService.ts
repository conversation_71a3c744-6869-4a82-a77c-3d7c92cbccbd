import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export interface VoiceSettings {
  speechToText: {
    enabled: boolean;
    provider: 'openai' | 'google' | 'azure' | 'disabled';
    apiKeyConfigured: boolean;
    language: string;
    autoDetectLanguage: boolean;
  };
  textToSpeech: {
    enabled: boolean;
    provider: 'openai' | 'google' | 'azure' | 'elevenlabs' | 'disabled';
    apiKeyConfigured: boolean;
    voice: string;
    speed: number;
    language: string;
  };
  voiceChat: {
    enabled: boolean;
    autoPlay: boolean;
    showTranscript: boolean;
    enableInterruption: boolean;
    maxRecordingTime: number;
  };
  whatsappVoice: {
    enabled: boolean;
    apiKeyConfigured: boolean;
    autoTranscribe: boolean;
    autoRespond: boolean;
  };
  general: {
    enabledForGuests: boolean;
    enabledForUsers: boolean;
    enabledForAdmin: boolean;
    fallbackToText: boolean;
    debugMode: boolean;
  };
}

export interface VoiceCapabilities {
  speechToText: boolean;
  textToSpeech: boolean;
  voiceChat: boolean;
  whatsappVoice: boolean;
  realTimeProcessing: boolean;
}

export interface VoiceUsageStats {
  totalRequests: number;
  speechToTextRequests: number;
  textToSpeechRequests: number;
  averageProcessingTime: number;
  errorRate: number;
  costThisMonth: number;
  requestsThisMonth: number;
  topLanguages: Array<{
    language: string;
    count: number;
    percentage: number;
  }>;
}

class VoiceSettingsService {
  private baseURL: string;

  constructor() {
    this.baseURL = `${API_BASE_URL}/admin/voice-settings`;
  }

  // Get current voice settings
  async getSettings(): Promise<VoiceSettings> {
    try {
      const response = await axios.get(`${this.baseURL}`);
      return response.data.data;
    } catch (error: any) {
      console.error('Error fetching voice settings:', error);
      // Return mock data for development
      return this.getMockSettings();
    }
  }

  // Update voice settings
  async updateSettings(settings: Partial<VoiceSettings>): Promise<VoiceSettings> {
    try {
      const response = await axios.patch(`${this.baseURL}`, settings);
      return response.data.data;
    } catch (error: any) {
      console.error('Error updating voice settings:', error);
      throw new Error(
        error.response?.data?.message || 
        error.message || 
        'Failed to update voice settings'
      );
    }
  }

  // Test voice capabilities
  async testCapabilities(): Promise<VoiceCapabilities> {
    try {
      const response = await axios.post(`${this.baseURL}/test`);
      return response.data.data;
    } catch (error: any) {
      console.error('Error testing voice capabilities:', error);
      // Return mock data for development
      return this.getMockCapabilities();
    }
  }

  // Get voice usage statistics
  async getUsageStats(): Promise<VoiceUsageStats> {
    try {
      const response = await axios.get(`${this.baseURL}/stats`);
      return response.data.data;
    } catch (error: any) {
      console.error('Error fetching voice usage stats:', error);
      // Return mock data for development
      return this.getMockUsageStats();
    }
  }

  // Validate API keys
  async validateApiKeys(): Promise<{
    openai: boolean;
    google: boolean;
    azure: boolean;
    elevenlabs: boolean;
  }> {
    try {
      const response = await axios.post(`${this.baseURL}/validate-keys`);
      return response.data.data;
    } catch (error: any) {
      console.error('Error validating API keys:', error);
      // Return mock data for development
      return {
        openai: false,
        google: false,
        azure: false,
        elevenlabs: false
      };
    }
  }

  // Get available voices for TTS
  async getAvailableVoices(provider: string): Promise<Array<{
    id: string;
    name: string;
    language: string;
    gender: 'male' | 'female' | 'neutral';
    preview?: string;
  }>> {
    try {
      const response = await axios.get(`${this.baseURL}/voices/${provider}`);
      return response.data.data;
    } catch (error: any) {
      console.error('Error fetching available voices:', error);
      // Return mock data for development
      return this.getMockVoices();
    }
  }

  // Mock data for development
  private getMockSettings(): VoiceSettings {
    return {
      speechToText: {
        enabled: false, // Disabled by default until API keys are configured
        provider: 'openai',
        apiKeyConfigured: false,
        language: 'en-US',
        autoDetectLanguage: true
      },
      textToSpeech: {
        enabled: false, // Disabled by default until API keys are configured
        provider: 'openai',
        apiKeyConfigured: false,
        voice: 'alloy',
        speed: 1.0,
        language: 'en-US'
      },
      voiceChat: {
        enabled: false, // Disabled by default
        autoPlay: true,
        showTranscript: true,
        enableInterruption: true,
        maxRecordingTime: 60
      },
      whatsappVoice: {
        enabled: false, // Disabled by default
        apiKeyConfigured: false,
        autoTranscribe: true,
        autoRespond: false
      },
      general: {
        enabledForGuests: false,
        enabledForUsers: true,
        enabledForAdmin: true,
        fallbackToText: true,
        debugMode: false
      }
    };
  }

  private getMockCapabilities(): VoiceCapabilities {
    return {
      speechToText: false,
      textToSpeech: false,
      voiceChat: false,
      whatsappVoice: false,
      realTimeProcessing: false
    };
  }

  private getMockUsageStats(): VoiceUsageStats {
    return {
      totalRequests: 0,
      speechToTextRequests: 0,
      textToSpeechRequests: 0,
      averageProcessingTime: 0,
      errorRate: 0,
      costThisMonth: 0,
      requestsThisMonth: 0,
      topLanguages: [
        { language: 'English', count: 0, percentage: 0 },
        { language: 'Spanish', count: 0, percentage: 0 }
      ]
    };
  }

  private getMockVoices() {
    return [
      {
        id: 'alloy',
        name: 'Alloy',
        language: 'en-US',
        gender: 'neutral' as const
      },
      {
        id: 'echo',
        name: 'Echo',
        language: 'en-US',
        gender: 'male' as const
      },
      {
        id: 'fable',
        name: 'Fable',
        language: 'en-US',
        gender: 'female' as const
      },
      {
        id: 'onyx',
        name: 'Onyx',
        language: 'en-US',
        gender: 'male' as const
      },
      {
        id: 'nova',
        name: 'Nova',
        language: 'en-US',
        gender: 'female' as const
      },
      {
        id: 'shimmer',
        name: 'Shimmer',
        language: 'en-US',
        gender: 'female' as const
      }
    ];
  }
}

export const voiceSettingsService = new VoiceSettingsService();
