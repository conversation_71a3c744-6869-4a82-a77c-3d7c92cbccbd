import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';

// Configuration
const ALIEXPRESS_APP_KEY = process.env.ALIEXPRESS_APP_KEY || '515696';
const ALIEXPRESS_APP_SECRET = process.env.ALIEXPRESS_APP_SECRET || 'Hd6k8SLMPyXZMi7LYTCqzZv4Dn4uWzrW';
const CALLBACK_URL = process.env.ALIEXPRESS_REDIRECT_URI || 'http://vmi1667694.contaboserver.net:8090/api/auth/aliexpress/callback';

// AliExpress token exchange URL (New Open Platform)
const TOKEN_URL = 'https://api-sg.aliexpress.com/rest';

export async function GET(request: NextRequest) {
  console.log('🔄 Processing AliExpress OAuth callback');
  
  const { searchParams } = new URL(request.url);
  const code = searchParams.get('code');
  const state = searchParams.get('state');
  const error = searchParams.get('error');
  
  // Check for OAuth errors
  if (error) {
    console.error('❌ OAuth error received:', error);
    return NextResponse.redirect(
      new URL('/admin/settings?aliexpress=error&message=' + encodeURIComponent(error), request.url)
    );
  }
  
  // Validate required parameters
  if (!code || !state) {
    console.error('❌ Missing required OAuth parameters');
    return NextResponse.redirect(
      new URL('/admin/settings?aliexpress=error&message=missing_parameters', request.url)
    );
  }
  
  // Validate state for CSRF protection (optional but recommended)
  const storedState = request.cookies.get('aliexpress_oauth_state')?.value;
  if (storedState !== state) {
    console.error('❌ OAuth state mismatch - possible CSRF attack');
    return NextResponse.redirect(
      new URL('/admin/settings?aliexpress=error&message=state_mismatch', request.url)
    );
  }
  
  try {
    console.log('🔑 Exchanging authorization code for access token');
    
    // Exchange authorization code for access token
    const tokenResponse = await exchangeCodeForToken(code);
    
    if (tokenResponse.success) {
      console.log('✅ Successfully obtained access token');
      
      // Store token securely (implement your storage logic)
      // For now, we'll redirect to admin with success
      const response = NextResponse.redirect(
        new URL('/admin/settings?aliexpress=success&message=connected', request.url)
      );
      
      // Clear the OAuth state cookie
      response.cookies.delete('aliexpress_oauth_state');
      
      return response;
    } else {
      console.error('❌ Token exchange failed:', tokenResponse.error);
      return NextResponse.redirect(
        new URL('/admin/settings?aliexpress=error&message=token_exchange_failed', request.url)
      );
    }
    
  } catch (error) {
    console.error('❌ Callback processing failed:', error);
    return NextResponse.redirect(
      new URL('/admin/settings?aliexpress=error&message=callback_failed', request.url)
    );
  }
}

async function exchangeCodeForToken(code: string) {
  try {
    // Build request parameters for token exchange
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const method = 'aliexpress.system.oauth2.integrationToken.get';
    
    const params: any = {
      app_key: ALIEXPRESS_APP_KEY,
      timestamp,
      method,
      format: 'json',
      v: '2.0',
      sign_method: 'md5',
      code,
      uuid: crypto.randomUUID()
    };
    
    // Generate signature (implement proper MD5 signature generation)
    const signature = generateSignature(params, ALIEXPRESS_APP_SECRET);
    params.sign = signature;
    
    // Make token exchange request
    const response = await fetch(TOKEN_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams(params).toString()
    });
    
    const data = await response.json();
    
    if (data.error_response) {
      return {
        success: false,
        error: data.error_response
      };
    }
    
    return {
      success: true,
      data: data.aliexpress_system_oauth2_integrationToken_get_response
    };
    
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

function generateSignature(params: any, secret: string): string {
  // Sort parameters by key
  const sortedParams = Object.keys(params)
    .filter(key => key !== 'sign')
    .sort()
    .map(key => `${key}${params[key]}`)
    .join('');
  
  // Create signature string
  const signString = secret + sortedParams + secret;
  
  // Generate MD5 hash
  const hash = crypto.createHash('md5').update(signString).digest('hex');
  return hash.toUpperCase();
}

export async function POST(request: NextRequest) {
  // Handle POST requests if needed
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
} 