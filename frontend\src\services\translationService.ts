import { Locale } from '@/i18n/config';

interface TranslationCache {
  [key: string]: {
    [locale: string]: string;
    timestamp: number;
  };
}

interface TranslationRequest {
  text: string;
  fromLanguage: string;
  toLanguage: string;
  context?: 'product' | 'description' | 'category' | 'general';
}

interface TranslationResponse {
  translatedText: string;
  confidence: number;
  detectedLanguage?: string;
}

class TranslationService {
  private cache: TranslationCache = {};
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
  private readonly API_ENDPOINTS = {
    google: 'https://translation.googleapis.com/language/translate/v2',
    openai: '/api/translate/openai',
    deepl: 'https://api-free.deepl.com/v2/translate'
  };

  constructor() {
    this.loadCacheFromStorage();
  }

  // Main translation method
  async translate(
    text: string, 
    targetLanguage: Locale, 
    sourceLanguage: string = 'auto',
    context: 'product' | 'description' | 'category' | 'general' = 'general'
  ): Promise<string> {
    if (!text || text.trim() === '') return text;
    
    // Check cache first
    const cacheKey = this.getCacheKey(text, sourceLanguage, targetLanguage);
    const cached = this.getFromCache(cacheKey);
    if (cached) return cached;

    try {
      // Try multiple translation services in order of preference
      let result = await this.translateWithOpenAI(text, targetLanguage, sourceLanguage, context);
      
      if (!result) {
        result = await this.translateWithGoogle(text, targetLanguage, sourceLanguage);
      }
      
      if (!result) {
        result = await this.translateWithDeepL(text, targetLanguage, sourceLanguage);
      }

      if (result) {
        this.saveToCache(cacheKey, result);
        return result;
      }

      // Fallback to original text if all services fail
      console.warn('All translation services failed, returning original text');
      return text;
    } catch (error) {
      console.error('Translation error:', error);
      return text;
    }
  }

  // OpenAI translation (best for context-aware translations)
  private async translateWithOpenAI(
    text: string, 
    targetLanguage: Locale, 
    sourceLanguage: string,
    context: string
  ): Promise<string | null> {
    try {
      const response = await fetch('/api/translate/openai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          targetLanguage,
          sourceLanguage,
          context
        })
      });

      if (!response.ok) throw new Error('OpenAI translation failed');

      const data = await response.json();
      return data.translatedText;
    } catch (error) {
      console.error('OpenAI translation error:', error);
      return null;
    }
  }

  // Google Translate
  private async translateWithGoogle(
    text: string, 
    targetLanguage: Locale, 
    sourceLanguage: string
  ): Promise<string | null> {
    try {
      const apiKey = process.env.NEXT_PUBLIC_GOOGLE_TRANSLATE_API_KEY;
      if (!apiKey) return null;

      const response = await fetch(
        `${this.API_ENDPOINTS.google}?key=${apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            q: text,
            target: targetLanguage,
            source: sourceLanguage === 'auto' ? undefined : sourceLanguage,
            format: 'text'
          })
        }
      );

      if (!response.ok) throw new Error('Google Translate failed');

      const data = await response.json();
      return data.data.translations[0].translatedText;
    } catch (error) {
      console.error('Google Translate error:', error);
      return null;
    }
  }

  // DeepL translation
  private async translateWithDeepL(
    text: string, 
    targetLanguage: Locale, 
    sourceLanguage: string
  ): Promise<string | null> {
    try {
      const apiKey = process.env.NEXT_PUBLIC_DEEPL_API_KEY;
      if (!apiKey) return null;

      const response = await fetch(this.API_ENDPOINTS.deepl, {
        method: 'POST',
        headers: {
          'Authorization': `DeepL-Auth-Key ${apiKey}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          text,
          target_lang: targetLanguage.toUpperCase(),
          source_lang: sourceLanguage === 'auto' ? '' : sourceLanguage.toUpperCase()
        })
      });

      if (!response.ok) throw new Error('DeepL translation failed');

      const data = await response.json();
      return data.translations[0].text;
    } catch (error) {
      console.error('DeepL translation error:', error);
      return null;
    }
  }

  // Batch translation for multiple texts
  async translateBatch(
    texts: string[], 
    targetLanguage: Locale, 
    sourceLanguage: string = 'auto',
    context: 'product' | 'description' | 'category' | 'general' = 'general'
  ): Promise<string[]> {
    const promises = texts.map(text => 
      this.translate(text, targetLanguage, sourceLanguage, context)
    );
    return Promise.all(promises);
  }

  // Translate product data from AliExpress
  async translateProduct(product: any, targetLanguage: Locale): Promise<any> {
    const translatedProduct = { ...product };

    try {
      // Translate product name
      if (product.name) {
        translatedProduct.name = await this.translate(
          product.name, 
          targetLanguage, 
          'auto', 
          'product'
        );
      }

      // Translate description
      if (product.description) {
        translatedProduct.description = await this.translate(
          product.description, 
          targetLanguage, 
          'auto', 
          'description'
        );
      }

      // Translate category
      if (product.category) {
        translatedProduct.category = await this.translate(
          product.category, 
          targetLanguage, 
          'auto', 
          'category'
        );
      }

      // Translate tags
      if (product.tags && Array.isArray(product.tags)) {
        translatedProduct.tags = await this.translateBatch(
          product.tags, 
          targetLanguage, 
          'auto', 
          'general'
        );
      }

      // Translate variant names
      if (product.variants && Array.isArray(product.variants)) {
        translatedProduct.variants = await Promise.all(
          product.variants.map(async (variant: any) => ({
            ...variant,
            name: await this.translate(variant.name, targetLanguage, 'auto', 'product')
          }))
        );
      }

      return translatedProduct;
    } catch (error) {
      console.error('Product translation error:', error);
      return product; // Return original if translation fails
    }
  }

  // Detect language of text
  async detectLanguage(text: string): Promise<string> {
    try {
      const response = await fetch('/api/translate/detect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text })
      });

      if (!response.ok) throw new Error('Language detection failed');

      const data = await response.json();
      return data.language;
    } catch (error) {
      console.error('Language detection error:', error);
      return 'en'; // Default to English
    }
  }

  // Cache management
  private getCacheKey(text: string, from: string, to: string): string {
    return `${from}-${to}-${btoa(text.substring(0, 100))}`;
  }

  private getFromCache(key: string): string | null {
    const cached = this.cache[key];
    if (!cached) return null;

    // Check if cache is expired
    if (Date.now() - cached.timestamp > this.CACHE_DURATION) {
      delete this.cache[key];
      this.saveCacheToStorage();
      return null;
    }

    return cached[Object.keys(cached).find(k => k !== 'timestamp') || ''] || null;
  }

  private saveToCache(key: string, translation: string): void {
    this.cache[key] = {
      [key.split('-')[1]]: translation,
      timestamp: Date.now()
    };
    this.saveCacheToStorage();
  }

  private loadCacheFromStorage(): void {
    if (typeof window === 'undefined') return;
    
    try {
      const cached = localStorage.getItem('translation-cache');
      if (cached) {
        this.cache = JSON.parse(cached);
      }
    } catch (error) {
      console.error('Failed to load translation cache:', error);
      this.cache = {};
    }
  }

  private saveCacheToStorage(): void {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem('translation-cache', JSON.stringify(this.cache));
    } catch (error) {
      console.error('Failed to save translation cache:', error);
    }
  }

  // Clear cache
  clearCache(): void {
    this.cache = {};
    if (typeof window !== 'undefined') {
      localStorage.removeItem('translation-cache');
    }
  }

  // Get translation statistics
  getStats(): { cacheSize: number; cacheHits: number } {
    return {
      cacheSize: Object.keys(this.cache).length,
      cacheHits: 0 // Would need to implement hit tracking
    };
  }
}

// Create singleton instance
export const translationService = new TranslationService();

// Hook for using translation service in React components
export function useTranslation() {
  return {
    translate: translationService.translate.bind(translationService),
    translateBatch: translationService.translateBatch.bind(translationService),
    translateProduct: translationService.translateProduct.bind(translationService),
    detectLanguage: translationService.detectLanguage.bind(translationService),
    clearCache: translationService.clearCache.bind(translationService),
    getStats: translationService.getStats.bind(translationService)
  };
}
