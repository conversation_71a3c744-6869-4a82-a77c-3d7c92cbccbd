'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import MainLayout from '@/components/layout/MainLayout';
import { StarIcon, DiamondIcon, ShippingIcon } from '@/components/ui/Icons';
import UnifiedProductService from '@/services/unifiedProductService';
import { StoreProduct } from '@/services/productSync';

function SearchContent() {
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState(searchParams?.get('q') || '');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedPriceRange, setSelectedPriceRange] = useState({ label: 'All Prices', min: 0, max: Infinity });
  const [filteredProducts, setFilteredProducts] = useState<StoreProduct[]>([]);
  const [categories, setCategories] = useState<string[]>(['All']);
  const [priceRanges, setPriceRanges] = useState([
    { label: 'All Prices', min: 0, max: Infinity },
    { label: 'Under $50', min: 0, max: 50 },
    { label: '$50 - $100', min: 50, max: 100 },
    { label: '$100 - $200', min: 100, max: 200 },
    { label: 'Over $200', min: 200, max: Infinity }
  ]);
  const [loading, setLoading] = useState(true);

  // Load categories and price ranges on mount
  useEffect(() => {
    const loadFilters = async () => {
      try {
        const [categoriesData, priceRangesData] = await Promise.all([
          UnifiedProductService.getCategories(),
          UnifiedProductService.getPriceRanges()
        ]);
        
        setCategories(['All', ...categoriesData]);
        setPriceRanges([
          { label: 'All Prices', min: 0, max: Infinity },
          ...priceRangesData.map(range => ({
            label: `${range.label} (${range.count})`,
            min: range.min,
            max: range.max
          }))
        ]);
      } catch (error) {
        console.error('Error loading filters:', error);
      }
    };

    loadFilters();
  }, []);

  // Filter products based on search and filters
  useEffect(() => {
    const searchProducts = async () => {
      setLoading(true);
      try {
        let products: StoreProduct[] = [];

        if (searchQuery.trim()) {
          // Use search functionality
          products = await UnifiedProductService.searchProducts(searchQuery, 50);
        } else {
          // Get all products for filtering
          const shopData = await UnifiedProductService.getShopProducts({ limit: 50 });
          products = shopData.products;
        }

        // Apply category filter
        if (selectedCategory !== 'All') {
          products = products.filter(product =>
            product.category.toLowerCase().includes(selectedCategory.toLowerCase()) ||
            product.subcategory.toLowerCase().includes(selectedCategory.toLowerCase())
          );
        }

        // Apply price filter
        if (selectedPriceRange.max !== Infinity || selectedPriceRange.min !== 0) {
          products = products.filter(product =>
            product.price >= selectedPriceRange.min && product.price <= selectedPriceRange.max
          );
        }

        setFilteredProducts(products);
      } catch (error) {
        console.error('Error searching products:', error);
        setFilteredProducts([]);
      } finally {
        setLoading(false);
      }
    };

    searchProducts();
  }, [searchQuery, selectedCategory, selectedPriceRange]);

  return (
    <MainLayout>
      <div className="pt-20 min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Search Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Search Results
              {searchQuery && (
                <span className="text-primary"> for "{searchQuery}"</span>
              )}
            </h1>
            <p className="text-gray-600">
              {loading ? 'Searching...' : `${filteredProducts.length} products found`}
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-4 gap-8">
            {/* Filters Sidebar */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="lg:col-span-1"
            >
              <div className="bg-white rounded-xl shadow-sm p-6 sticky top-24">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Filters</h3>

                {/* Search Input */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Search
                  </label>
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search for wigs..."
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                {/* Category Filter */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <label key={category} className="flex items-center">
                        <input
                          type="radio"
                          name="category"
                          value={category}
                          checked={selectedCategory === category}
                          onChange={(e) => setSelectedCategory(e.target.value)}
                          className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                        />
                        <span className="ml-2 text-sm text-gray-700">{category}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Price Filter */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Price Range
                  </label>
                  <div className="space-y-2">
                    {priceRanges.map((range, index) => (
                      <label key={index} className="flex items-center">
                        <input
                          type="radio"
                          name="priceRange"
                          checked={selectedPriceRange === range}
                          onChange={() => setSelectedPriceRange(range)}
                          className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                        />
                        <span className="ml-2 text-sm text-gray-700">{range.label}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Results Grid */}
            <div className="lg:col-span-3">
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-4 text-gray-600">Searching products...</p>
                </div>
              ) : filteredProducts.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-center py-12"
                >
                  <DiamondIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                  <p className="text-gray-600">Try adjusting your search or filters</p>
                </motion.div>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                >
                  {filteredProducts.map((product, index) => (
                    <motion.div
                      key={product.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-lg transition-shadow duration-300"
                    >
                      <div className="relative">
                        <img
                          src={product.images[0] || product.image}
                          alt={product.title}
                          className="w-full h-64 object-cover"
                        />
                        <div className="absolute top-4 right-4">
                          <button className="p-2 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow">
                            <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                          </button>
                        </div>
                        {product.discount > 0 && (
                          <div className="absolute top-4 left-4">
                            <span className="bg-red-500 text-white px-2 py-1 text-xs font-bold rounded">
                              -{product.discount}%
                            </span>
                          </div>
                        )}
                      </div>
                      
                      <div className="p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{product.title}</h3>
                        
                        <div className="flex items-center mb-3">
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <StarIcon
                                key={i}
                                className={`w-4 h-4 ${
                                  i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="ml-2 text-sm text-gray-600">({product.reviews})</span>
                        </div>
                        
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-2">
                            <span className="text-xl font-bold text-gray-900">${product.price}</span>
                            {product.originalPrice && product.originalPrice > product.price && (
                              <span className="text-sm text-gray-500 line-through">${product.originalPrice}</span>
                            )}
                          </div>
                          <span className="px-2 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full">
                            {product.category}
                          </span>
                        </div>
                        
                        <div className="flex items-center text-sm text-gray-600 mb-4">
                          <ShippingIcon className="w-4 h-4 mr-1" />
                          <span>{product.shipping}</span>
                        </div>
                        
                        <button className="w-full bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary/90 transition-colors">
                          Add to Cart
                        </button>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <MainLayout>
        <div className="pt-20 min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading search results...</p>
          </div>
        </div>
      </MainLayout>
    }>
      <SearchContent />
    </Suspense>
  );
} 