'use client';

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion';
import InstagramFeed from '@/components/instagram/InstagramFeed';
import HomepageBlogSection from '@/components/blog/HomepageBlogSection';
import SectionDivider from '@/components/ui/SectionDivider';
import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { UnifiedProductService } from '@/services/unifiedProductService';
import { CompactLanguageSwitcher } from '@/contexts/LanguageContext';
import { CompactCurrencySwitcher, PriceDisplay } from '@/contexts/CurrencyContext';
import {
  StarIcon,
  ShippingIcon,
  DiamondIcon,
  ReturnIcon,
  HeartIcon,
  QualityIcon,
  FireIcon,
  GiftIcon,
  MoneyIcon,
  ScissorsIcon,
  ArrowRightIcon,
  CheckIcon,
  UsersIcon
} from '@/components/ui/Icons';

// Interface for products
interface StoreProduct {
  id: string;
  title: string;
  price: number;
  originalPrice: number;
  rating: number;
  reviews: number;
  sales: number;
  imageUrl: string;
  slug: string;
  category: string;
  subcategory: string;
  storeId: string;
  storeName: string;
  description: string;
  isNew: boolean;
  isFeatured: boolean;
}

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 60 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: 'easeOut' } }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.3
    }
  }
};

const staggerItem = {
  hidden: { opacity: 0, y: 30 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

// Shop by categories data with real wig images
const categories = [
  { 
    name: 'Lace Front Wigs', 
    count: '10+ styles', 
    image: 'https://images.unsplash.com/photo-1594736797933-d0501ba2fe65?w=400&h=400&fit=crop&crop=faces', 
    popular: true,
    link: '/shop/lace-front'
  },
  { 
    name: 'Bob Wigs', 
    count: '5+ styles', 
    image: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop&crop=faces', 
    popular: true,
    link: '/shop/bob'
  },
  { 
    name: 'Full Lace Wigs', 
    count: '5+ styles', 
    image: 'https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=400&h=400&fit=crop&crop=faces', 
    popular: true,
    link: '/shop/full-lace'
  },
  { 
    name: 'All Collections', 
    count: '20+ styles', 
    image: 'https://images.unsplash.com/photo-1487412947147-5cebf100ff38?w=400&h=400&fit=crop&crop=faces', 
    popular: true,
    link: '/collections'
  },
  { 
    name: 'Premium Store', 
    count: '10+ styles', 
    image: 'https://images.unsplash.com/photo-1595475038665-8b3f8c3d8092?w=400&h=400&fit=crop&crop=faces', 
    popular: false,
    link: '/collections/premium'
  },
  { 
    name: 'Catalog', 
    count: '20+ styles', 
    image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a5d?w=400&h=400&fit=crop&crop=faces', 
    popular: true,
    link: '/catalog'
  },
  { 
    name: 'New Arrivals', 
    count: '5+ styles', 
    image: 'https://images.unsplash.com/photo-1594736797933-d0501ba2fe65?w=400&h=400&fit=crop&crop=faces', 
    popular: false,
    link: '/catalog?sort=newest'
  },
  { 
    name: 'Best Sellers', 
    count: '10+ styles', 
    image: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400&h=400&fit=crop&crop=faces', 
    popular: true,
    link: '/catalog?sort=bestselling'
  }
];

export default function Home() {
  const { scrollY } = useScroll();
  const [currentSlide, setCurrentSlide] = useState(0);
  
  // Hero slider data will be populated from real products
  const [heroSlides, setHeroSlides] = useState([
  {
      id: 1,
      title: "Lace Front Wigs",
      subtitle: "Undetectable Hairline",
      description: "Experience the most natural look with our premium lace front wigs from AliExpress stores.",
      image: "https://images.unsplash.com/photo-1594736797933-d0501ba2fe65?w=800&h=600&fit=crop&crop=faces",
      price: "From $67",
      offer: "Premium Quality",
      cta: "Shop Lace Front",
      link: "/shop/lace-front"
  },
  {
      id: 2,
      title: "Full Lace Wigs",
      subtitle: "360° Styling Freedom",
      description: "Complete versatility with full lace construction from our partner stores.",
      image: "https://images.unsplash.com/photo-1560472354-b33ff0c44a5d?w=800&h=600&fit=crop&crop=faces",
      price: "From $119",
      offer: "Top Rated",
      cta: "Shop Full Lace",
      link: "/shop/full-lace"
    },
    {
      id: 3,
      title: "Bob Wigs",
      subtitle: "Chic & Trendy",
      description: "Modern bob styles from Elite Beauty Store and Premium Hair Collection.",
      image: "https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=800&h=600&fit=crop&crop=faces",
      price: "From $67",
      offer: "NEW ARRIVAL",
      cta: "Shop Bob Wigs",
      link: "/shop/bob"
    }
  ]);
  
  // Real product data state
  const [featuredProducts, setFeaturedProducts] = useState<StoreProduct[]>([]);
  const [trendingProducts, setTrendingProducts] = useState<StoreProduct[]>([]);
  const [newArrivals, setNewArrivals] = useState<StoreProduct[]>([]);
  const [productsLoading, setProductsLoading] = useState(true);
  
  // Parallax effects
  const heroY = useTransform(scrollY, [0, 500], [0, -150]);
  const bgY = useTransform(scrollY, [0, 500], [0, -100]);

  // Fetch real products from unified service
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setProductsLoading(true);
        
        // Fetch different product categories
        const [featured, trending, arrivals] = await Promise.all([
          UnifiedProductService.getHomepageFeaturedProducts(6),
          UnifiedProductService.getTrendingProducts(8),
          UnifiedProductService.getNewArrivals(6)
        ]);
        
        setFeaturedProducts(featured);
        setTrendingProducts(trending);
        setNewArrivals(arrivals);
      } catch (error) {
        console.error('Error fetching homepage products:', error);
      } finally {
        setProductsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  // Auto-advance hero slider
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  // Section animation hooks
  const { ref: heroRef, isInView: heroInView } = useScrollAnimation({ threshold: 0.3 });
  const { ref: categoriesRef, isInView: categoriesInView } = useScrollAnimation();
  const { ref: bestsellersRef, isInView: bestsellersInView } = useScrollAnimation();
  const { ref: offersRef, isInView: offersInView } = useScrollAnimation();
  const { ref: featuresRef, isInView: featuresInView } = useScrollAnimation();

  return (
    <div className="overflow-hidden">
      {/* Language and Currency Switchers - Fixed Position */}
      <div className="fixed top-20 right-4 z-50 flex items-center space-x-2">
        <CompactLanguageSwitcher className="bg-white/90 backdrop-blur-sm shadow-lg rounded-lg" />
        <CompactCurrencySwitcher className="bg-white/90 backdrop-blur-sm shadow-lg rounded-lg" />
      </div>
        {/* Enhanced Hero Section with Animated Background and Slider */}
        <section 
          ref={heroRef}
          className="relative min-h-screen flex items-center overflow-hidden bg-gradient-to-br from-slate-50 via-white to-pink-50"
        >
          {/* Advanced Animated Background */}
          <motion.div 
            className="absolute inset-0 opacity-40"
            style={{ y: bgY }}
          >
            {/* Floating Gradient Orbs with Advanced Animation */}
            {[...Array(8)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute rounded-full blur-3xl"
                style={{
                  left: `${5 + i * 12}%`,
                  top: `${10 + (i % 4) * 20}%`,
                  width: `${100 + i * 20}px`,
                  height: `${100 + i * 20}px`,
                  background: i % 3 === 0 
                    ? 'linear-gradient(135deg, rgba(240, 98, 146, 0.3), rgba(186, 104, 200, 0.2))'
                    : i % 3 === 1
                    ? 'linear-gradient(135deg, rgba(186, 104, 200, 0.3), rgba(100, 181, 246, 0.2))'
                    : 'linear-gradient(135deg, rgba(100, 181, 246, 0.3), rgba(240, 98, 146, 0.2))'
                }}
                animate={{
                  y: [0, -30, 0],
                  x: [0, 15, 0],
                  scale: [1, 1.2, 1],
                  opacity: [0.2, 0.4, 0.2],
                }}
                transition={{
                  duration: 8 + i * 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: i * 0.5
                }}
              />
            ))}

            {/* Geometric Shapes Animation */}
            <svg 
              className="absolute top-0 left-0 w-full h-full opacity-10" 
              viewBox="0 0 1000 1000"
            >
              {[...Array(12)].map((_, i) => (
                <motion.circle
                  key={i}
                  cx={100 + (i % 4) * 250}
                  cy={100 + Math.floor(i / 4) * 300}
                  r={3 + (i % 3) * 2}
                  fill="url(#gradient)"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.3, 0.8, 0.3],
                  }}
                  transition={{
                    duration: 3 + (i % 4),
                    repeat: Infinity,
                    delay: i * 0.2,
                  }}
                />
              ))}
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#f06292" />
                  <stop offset="50%" stopColor="#ba68c8" />
                  <stop offset="100%" stopColor="#64b5f6" />
                </linearGradient>
              </defs>
            </svg>
          </motion.div>

          {/* Hero Content */}
          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
            <div className="grid lg:grid-cols-2 gap-12 items-center h-full min-h-screen">
              {/* Left Content */}
              <motion.div 
                className="space-y-8 text-left"
                style={{ y: heroY }}
                initial="hidden"
                animate={heroInView ? "visible" : "hidden"}
                variants={staggerContainer}
              >
                {/* Premium Quality Badge */}
                <motion.div variants={staggerItem}>
                  <motion.div 
                    className="inline-flex items-center space-x-3 px-6 py-4 bg-gradient-to-r from-primary to-secondary text-white font-bold rounded-full shadow-xl"
                    whileHover={{ scale: 1.05 }}
                    animate={{ scale: [1, 1.02, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <DiamondIcon className="w-6 h-6 text-yellow-300" />
                    <span className="text-lg">PREMIUM HUMAN HAIR COLLECTION</span>
                  </motion.div>
                </motion.div>

                {/* Dynamic Hero Content */}
                <AnimatePresence mode="wait">
                  <motion.div 
                    key={currentSlide}
                    variants={staggerItem}
                    initial={{ opacity: 0, x: -50 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 50 }}
                    transition={{ duration: 0.8 }}
                    className="space-y-6"
                  >
                    <h1 className="font-display text-5xl md:text-6xl lg:text-7xl font-bold text-dark leading-tight">
                      <motion.span 
                        className="bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent"
                      >
                        {heroSlides[currentSlide].title}
                      </motion.span>
                      <br />
                      <span className="text-4xl md:text-5xl lg:text-6xl text-gray-700">
                        {heroSlides[currentSlide].subtitle}
                      </span>
            </h1>

                    <p className="text-xl md:text-2xl text-gray-600 leading-relaxed">
                      {heroSlides[currentSlide].description}
                    </p>

                    <div className="flex items-center space-x-4">
                      <div className="text-3xl font-bold text-primary">
                        <PriceDisplay
                          amount={parseFloat(heroSlides[currentSlide].price.replace(/[^0-9.]/g, ''))}
                          originalCurrency="USD"
                          className="text-3xl font-bold text-primary"
                        />
                      </div>
                      <span className="px-4 py-2 bg-blue-100 text-blue-800 text-sm font-bold rounded-full flex items-center space-x-1">
                        <DiamondIcon className="w-4 h-4" />
                        <span>{heroSlides[currentSlide].offer}</span>
                      </span>
                    </div>
                  </motion.div>
                </AnimatePresence>

                {/* Enhanced CTA Buttons */}
                <motion.div variants={staggerItem} className="flex flex-col sm:flex-row gap-4 items-start">
                  <Link href={heroSlides[currentSlide].link}>
                  <motion.button 
                    className="group relative px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white font-bold rounded-full overflow-hidden shadow-xl"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-secondary to-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    />
                    <span className="relative z-10 flex items-center space-x-2">
                      <span>{heroSlides[currentSlide].cta}</span>
                      <motion.div
                        whileHover={{ x: 5 }}
                      >
                        <ArrowRightIcon className="w-5 h-5" />
                      </motion.div>
                    </span>
                  </motion.button>
                  </Link>
                  
                  <motion.button 
                    className="px-8 py-4 bg-white border-2 border-primary text-primary font-bold rounded-full hover:bg-primary hover:text-white transition-all duration-300 shadow-lg"
                    whileHover={{ scale: 1.05 }}
                  >
                    Try Virtual Fitting
                  </motion.button>
                </motion.div>

                {/* Trust Indicators with Real Icons */}
                <motion.div variants={staggerItem} className="flex flex-wrap gap-6 pt-4">
                  {[
                    { icon: StarIcon, text: '10K+ Happy Customers', color: 'text-yellow-600' },
                    { icon: ShippingIcon, text: 'Free 72Hr Delivery', color: 'text-blue-600' },
                    { icon: DiamondIcon, text: '100% Human Hair', color: 'text-purple-600' },
                    { icon: ReturnIcon, text: '30-Day Return', color: 'text-green-600' }
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center space-x-2"
                      whileHover={{ scale: 1.1 }}
                    >
                      <item.icon className={`w-6 h-6 ${item.color}`} />
                      <span className={`font-semibold text-sm ${item.color}`}>{item.text}</span>
                    </motion.div>
                  ))}
                </motion.div>
              </motion.div>

              {/* Right Side - Hero Image Slider */}
              <motion.div 
                className="relative hidden lg:block"
                initial={{ x: 100, opacity: 0 }}
                animate={heroInView ? { x: 0, opacity: 1 } : {}}
                transition={{ duration: 1.2, delay: 0.5 }}
              >
                <div className="relative h-[600px] rounded-3xl overflow-hidden shadow-2xl">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={currentSlide}
                      initial={{ opacity: 0, scale: 1.1 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.9 }}
                      transition={{ duration: 0.8 }}
                      className="absolute inset-0"
                    >
                      <img
                        src={heroSlides[currentSlide].image}
                        alt={heroSlides[currentSlide].title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // Fallback to a gradient background
                          e.currentTarget.style.display = 'none';
                          const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                          if (nextElement) {
                            nextElement.style.display = 'flex';
                          }
                        }}
                      />
                      {/* Fallback */}
                      <div 
                        className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center"
                        style={{ display: 'none' }}
                      >
                        <DiamondIcon className="w-24 h-24 text-white/60" />
            </div>
                    </motion.div>
                  </AnimatePresence>
                  
                  {/* Slider Navigation */}
                  <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    {heroSlides.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentSlide(index)}
                        className={`w-3 h-3 rounded-full transition-all duration-300 ${
                          index === currentSlide ? 'bg-white scale-125' : 'bg-white/50'
                        }`}
                      />
                    ))}
                </div>
                </div>
              </motion.div>
              </div>
                </div>
        </section>

        {/* Shop by Categories Section */}
        <section ref={categoriesRef} className="py-24 bg-gradient-to-br from-slate-50 via-white to-pink-50 relative">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-30">
            <svg className="w-full h-full" viewBox="0 0 200 200" fill="none">
              {[...Array(40)].map((_, i) => (
                <motion.circle
                  key={i}
                  cx={10 + (i % 10) * 20}
                  cy={10 + Math.floor(i / 10) * 20}
                  r="1"
                  fill="url(#categoryGradient)"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.2, 0.5, 0.2],
                  }}
                  transition={{
                    duration: 3 + (i % 4),
                    repeat: Infinity,
                    delay: i * 0.1,
                  }}
                />
              ))}
              <defs>
                <linearGradient id="categoryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#f06292" />
                  <stop offset="100%" stopColor="#ba68c8" />
                </linearGradient>
              </defs>
            </svg>
                </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate={categoriesInView ? "visible" : "hidden"}
              variants={staggerContainer}
              className="text-center mb-20"
            >
              <motion.div variants={staggerItem}>
                <motion.h2 
                  className="font-display text-5xl md:text-6xl lg:text-7xl font-normal text-dark mb-6"
                  initial={{ opacity: 0, y: 30 }}
                  animate={categoriesInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  Shop by{' '}
                  <motion.span 
                    className="font-bold bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent relative"
                    initial={{ backgroundPosition: '0%' }}
                    animate={{ backgroundPosition: ['0%', '100%', '0%'] }}
                    transition={{ duration: 4, repeat: Infinity, ease: 'easeInOut' }}
                  >
                    Categories
                    <motion.div
                      className="absolute -bottom-2 left-0 h-1 bg-gradient-to-r from-primary to-secondary rounded-full"
                      initial={{ width: '0%' }}
                      animate={categoriesInView ? { width: '100%' } : {}}
                      transition={{ duration: 1.2, delay: 0.8 }}
                    />
                  </motion.span>
                </motion.h2>
                <motion.p 
                  className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed"
                  initial={{ opacity: 0, y: 20 }}
                  animate={categoriesInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  Discover our exquisite collection of premium wig styles, each crafted with precision and designed for your unique beauty
                </motion.p>
              </motion.div>
            </motion.div>

            <motion.div
              initial="hidden"
              animate={categoriesInView ? "visible" : "hidden"}
              variants={staggerContainer}
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8"
            >
              {categories.map((category, index) => (
                <Link href={category.link} key={index}>
                <motion.div
                  variants={{
                    hidden: { opacity: 0, y: 60, scale: 0.9 },
                    visible: { 
                      opacity: 1, 
                      y: 0, 
                      scale: 1,
                      transition: { 
                        duration: 0.8, 
                        delay: index * 0.15,
                        ease: [0.25, 0.46, 0.45, 0.94]
                      }
                    }
                  }}
                  className="group relative cursor-pointer"
                  whileHover={{ y: -8 }}
                  transition={{ duration: 0.4, ease: 'easeOut' }}
                >
                  {/* Card with gradient border */}
                  <motion.div
                    className="relative bg-white rounded-[20px] overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500"
                    style={{
                      background: 'linear-gradient(145deg, #ffffff, #f8f9fa)',
                      border: '1px solid transparent',
                      backgroundClip: 'padding-box'
                    }}
                    whileHover={{
                      boxShadow: '0 25px 50px -12px rgba(240, 98, 146, 0.25)',
                    }}
                  >
                    {/* Gradient border effect */}
                    <div className="absolute inset-0 rounded-[20px] p-[1px] bg-gradient-to-br from-primary/30 via-secondary/20 to-accent/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="w-full h-full bg-white rounded-[19px]" />
              </div>

                    {/* Popular badge */}
                    {category.popular && (
                      <motion.div 
                        className="absolute top-4 left-4 z-20"
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{ duration: 0.6, delay: index * 0.1 + 0.5 }}
                      >
                        <motion.span 
                          className="px-3 py-1.5 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs font-bold rounded-full flex items-center space-x-1.5 shadow-lg"
                          animate={{ 
                            scale: [1, 1.05, 1],
                            rotate: [0, 2, -2, 0]
                          }}
                          transition={{ 
                            duration: 2,
                            repeat: Infinity,
                            ease: 'easeInOut'
                          }}
                        >
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                          >
                            <FireIcon className="w-3.5 h-3.5" />
                          </motion.div>
                          <span>Popular</span>
                        </motion.span>
                      </motion.div>
                    )}
                    
                    {/* Image container */}
                    <div className="relative aspect-[4/5] overflow-hidden">
                      <motion.div
                        className="absolute inset-0"
                        whileHover={{ scale: 1.08 }}
                        transition={{ duration: 0.6, ease: 'easeOut' }}
                      >
                        <img
                          src={category.image}
                          alt={category.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                            const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                            if (nextElement) {
                              nextElement.style.display = 'flex';
                            }
                          }}
                        />
                        {/* Fallback */}
                        <div 
                          className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center"
                          style={{ display: 'none' }}
                        >
                          <DiamondIcon className="w-16 h-16 text-white/60" />
                </div>
                      </motion.div>
                      
                      {/* Overlay gradient */}
                      <motion.div 
                        className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"
                        initial={{ opacity: 0 }}
                        whileHover={{ opacity: 1 }}
                        transition={{ duration: 0.4 }}
                      />

                      {/* Hover content */}
                      <motion.div
                        className="absolute inset-0 flex items-center justify-center"
                        initial={{ opacity: 0, y: 20 }}
                        whileHover={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: 0.1 }}
                      >
                        <Link href={category.link}>
                        <motion.button
                          className="px-6 py-3 bg-white/90 backdrop-blur-sm text-dark font-semibold rounded-full hover:bg-white transition-colors duration-300 shadow-lg"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          Shop Now
                        </motion.button>
                        </Link>
                      </motion.div>
                </div>

                    {/* Content */}
                    <div className="relative z-10 p-6 bg-white">
                      <motion.h3 
                        className="font-display text-xl md:text-2xl font-bold text-dark mb-2 group-hover:text-primary transition-colors duration-300"
                        whileHover={{ scale: 1.02 }}
                      >
                        {category.name}
                      </motion.h3>
                      <motion.p 
                        className="text-gray-500 font-medium"
                        initial={{ opacity: 0.7 }}
                        whileHover={{ opacity: 1 }}
                      >
                        {category.count}
                      </motion.p>

                      {/* Decorative line */}
                      <motion.div
                        className="mt-4 h-0.5 bg-gradient-to-r from-primary to-secondary rounded-full"
                        initial={{ width: '20%' }}
                        whileHover={{ width: '100%' }}
                        transition={{ duration: 0.4 }}
                      />
              </div>
                  </motion.div>
                </motion.div>
                </Link>
              ))}
            </motion.div>

            {/* Enhanced CTA Button */}
            <motion.div 
              className="text-center mt-16"
              initial={{ opacity: 0, y: 40 }}
              animate={categoriesInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 2, duration: 0.8 }}
            >
              <Link href="/catalog">
              <motion.button
                className="group relative inline-flex items-center space-x-3 px-10 py-5 bg-gradient-to-r from-primary via-secondary to-accent text-white font-bold text-lg rounded-full overflow-hidden shadow-xl"
                whileHover={{ 
                  scale: 1.05,
                  boxShadow: '0 20px 40px -12px rgba(240, 98, 146, 0.4)'
                }}
                whileTap={{ scale: 0.98 }}
                animate={{
                  background: [
                    'linear-gradient(45deg, #f06292, #ba68c8, #64b5f6)',
                    'linear-gradient(45deg, #ba68c8, #64b5f6, #f06292)',
                    'linear-gradient(45deg, #64b5f6, #f06292, #ba68c8)',
                    'linear-gradient(45deg, #f06292, #ba68c8, #64b5f6)'
                  ]
                }}
                transition={{ 
                  duration: 4,
                  repeat: Infinity,
                  ease: 'linear'
                }}
              >
                {/* Background pulse effect */}
                <motion.div
                  className="absolute inset-0 bg-white/20"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0, 0.3, 0]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: 'easeInOut'
                  }}
                />
                
                <span className="relative z-10">View All Categories</span>
                <motion.div 
                  className="relative z-10"
                  whileHover={{ x: 5 }}
                  transition={{ duration: 0.3 }}
                >
                  <ArrowRightIcon className="w-6 h-6" />
                </motion.div>

                {/* Shine effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                  initial={{ x: '-100%' }}
                  whileHover={{ x: '100%' }}
                  transition={{ duration: 0.6 }}
                />
              </motion.button>
              </Link>
            </motion.div>
            </div>
          </section>
          
        {/* Trending Bestsellers Section */}
        <section ref={bestsellersRef} className="py-20 bg-gradient-to-br from-slate-50 to-pink-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate={bestsellersInView ? "visible" : "hidden"}
              variants={staggerContainer}
              className="text-center mb-16"
            >
              <motion.div variants={staggerItem}>
                <h2 className="font-display text-4xl md:text-5xl font-bold text-dark mb-6">
                  Trending <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">Bestsellers</span>
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  Our most popular wigs loved by thousands of customers worldwide
                </p>
              </motion.div>
            </motion.div>

            <motion.div
              initial="hidden"
              animate={bestsellersInView ? "visible" : "hidden"}
              variants={staggerContainer}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {productsLoading ? (
                // Loading skeleton
                [...Array(6)].map((_, index) => (
                <motion.div
                  key={index}
                    variants={staggerItem}
                    className="bg-white rounded-2xl shadow-lg overflow-hidden animate-pulse"
                  >
                    <div className="aspect-square bg-gray-300"></div>
                    <div className="p-6 space-y-4">
                      <div className="h-6 bg-gray-300 rounded"></div>
                      <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                      <div className="h-8 bg-gray-300 rounded"></div>
                    </div>
                  </motion.div>
                ))
              ) : (
                trendingProducts.map((product, index) => (
                  <motion.div
                    key={product.id}
                  variants={staggerItem}
                  className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden"
                  whileHover={{ y: -8 }}
                >
                  {/* Product Badge */}
                  <div className="absolute top-4 left-4 z-10 flex space-x-2">
                    <span className="px-3 py-1 bg-blue-500 text-white text-xs font-bold rounded-full flex items-center space-x-1">
                      <ShippingIcon className="w-3 h-3" />
                        <span>Free Shipping</span>
                    </span>
                      {product.isFeatured && (
                        <span className="px-3 py-1 text-xs font-bold rounded-full bg-pink-100 text-pink-800">
                          Featured
                    </span>
                      )}
                      {product.isNew && (
                        <span className="px-3 py-1 text-xs font-bold rounded-full bg-blue-100 text-blue-800">
                          New
                        </span>
                      )}
                  </div>

                  {/* Stock Status */}
                  <div className="absolute top-4 right-4 z-10">
                      <span className="px-2 py-1 text-xs font-medium rounded-full flex items-center space-x-1 bg-green-100 text-green-800">
                      <CheckIcon className="w-3 h-3" />
                        <span>In Stock</span>
                    </span>
                </div>

                  {/* Product Image */}
                  <div className="aspect-square bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
                    <motion.div
                      className="absolute inset-0"
                      whileHover={{ scale: 1.1 }}
                      transition={{ duration: 0.6 }}
                    >
                      <img
                          src={product.imageUrl}
                          alt={product.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                          const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                          if (nextElement) {
                            nextElement.style.display = 'flex';
                          }
                        }}
                      />
                      <div 
                        className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center"
                        style={{ display: 'none' }}
                      >
                        <DiamondIcon className="w-16 h-16 text-white/60" />
                  </div>
                    </motion.div>
                </div>

                  {/* Product Info */}
                  <div className="p-6">
                      <h3 className="font-bold text-xl text-dark mb-2 group-hover:text-primary transition-colors line-clamp-2">
                        {product.title}
                    </h3>
                    
                    {/* Rating */}
                    <div className="flex items-center mb-3">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <StarIcon
                            key={i}
                            className={`w-4 h-4 ${i < Math.floor(product.rating) ? 'text-yellow-400' : 'text-gray-300'}`}
                          />
                        ))}
                  </div>
                      <span className="ml-2 text-sm text-gray-500">
                        {product.rating} ({product.reviews} reviews)
                      </span>
                </div>

                      {/* Store name */}
                      <div className="text-xs text-gray-500 mb-3">
                        {product.storeName} • {product.sales} sold
                      </div>

                    {/* Pricing */}
                    <div className="flex items-center space-x-2 mb-4">
                        <span className="text-2xl font-bold text-primary">${product.price}</span>
                        {product.originalPrice > product.price && (
                          <span className="text-lg text-gray-400 line-through">${product.originalPrice}</span>
                        )}
                        {product.originalPrice > product.price && (
                      <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-bold rounded flex items-center space-x-1">
                        <MoneyIcon className="w-3 h-3" />
                            <span>Save ${(product.originalPrice - product.price).toFixed(0)}</span>
                      </span>
                        )}
              </div>

                    {/* CTA Button */}
                    <motion.button
                        className="w-full py-3 font-bold rounded-full transition-all duration-300 bg-gradient-to-r from-primary to-secondary text-white hover:shadow-lg"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => window.location.href = `/product/${product.slug}`}
                      >
                        View Product
                    </motion.button>
              </div>
                </motion.div>
                ))
              )}
            </motion.div>

            <motion.div 
              className="text-center mt-12"
              initial={{ opacity: 0, y: 30 }}
              animate={bestsellersInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 1.5, duration: 0.6 }}
            >
              <Link href="/catalog">
              <motion.button
                className="inline-flex items-center space-x-2 px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white font-bold rounded-full hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.05 }}
              >
                <span>Shop All Wigs</span>
                <motion.div whileHover={{ x: 5 }}>
                  <ArrowRightIcon className="w-5 h-5" />
                </motion.div>
              </motion.button>
              </Link>
            </motion.div>
            </div>
          </section>
          
        {/* Company Values Section */}
        <section ref={offersRef} className="py-20 bg-gradient-to-r from-primary to-secondary text-white relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <svg className="w-full h-full" viewBox="0 0 400 400" fill="none">
              {[...Array(20)].map((_, i) => (
                <motion.circle
                  key={i}
                  cx={20 + (i % 10) * 40}
                  cy={20 + Math.floor(i / 10) * 40}
                  r="2"
                  fill="currentColor"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.3, 0.8, 0.3],
                  }}
                  transition={{
                    duration: 2 + (i % 3),
                    repeat: Infinity,
                    delay: i * 0.1,
                  }}
                />
              ))}
            </svg>
          </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial="hidden"
              animate={offersInView ? "visible" : "hidden"}
              variants={staggerContainer}
              className="text-center"
            >
              <motion.div variants={staggerItem} className="mb-8">
                <h2 className="font-display text-4xl md:text-6xl font-bold mb-4 flex items-center justify-center space-x-4">
                  <DiamondIcon className="w-12 h-12 text-yellow-300" />
                  <span>PREMIUM EXPERIENCE</span>
                  <DiamondIcon className="w-12 h-12 text-yellow-300" />
                </h2>
                <p className="text-xl md:text-2xl mb-6">
                  Exceptional Quality & Service You Deserve
                </p>
              </motion.div>

              <motion.div variants={staggerItem} className="grid md:grid-cols-3 gap-8 mb-12">
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
                  <DiamondIcon className="w-12 h-12 mx-auto mb-4 text-yellow-300" />
                  <h3 className="text-2xl font-bold mb-2">PREMIUM QUALITY</h3>
                  <p>100% Human Hair Collection</p>
                  </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
                  <ShippingIcon className="w-12 h-12 mx-auto mb-4 text-blue-200" />
                  <h3 className="text-2xl font-bold mb-2">FREE SHIPPING</h3>
                  <p>On orders over $200</p>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6">
                  <ReturnIcon className="w-12 h-12 mx-auto mb-4 text-green-200" />
                  <h3 className="text-2xl font-bold mb-2">30-DAY RETURNS</h3>
                  <p>Satisfaction guaranteed</p>
              </div>
              </motion.div>

              <motion.div variants={staggerItem}>
                <Link href="/collections">
                <motion.button
                  className="px-12 py-4 bg-white text-primary font-bold text-xl rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300"
                  whileHover={{ scale: 1.05, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
                  whileTap={{ scale: 0.95 }}
                >
                  EXPLORE COLLECTION
                </motion.button>
                </Link>
              </motion.div>
            </motion.div>
            </div>
          </section>
          
        {/* Enhanced Why Choose Pelucas Chic Section */}
        <section ref={featuresRef} className="py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50 relative overflow-hidden">
          {/* Floating Abstract Shapes with Parallax Effect */}
          <div className="absolute inset-0 opacity-20">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute rounded-full blur-2xl"
                style={{
                  left: `${10 + i * 15}%`,
                  top: `${5 + (i % 3) * 30}%`,
                  width: `${60 + i * 15}px`,
                  height: `${60 + i * 15}px`,
                  background: i % 2 === 0 
                    ? 'linear-gradient(135deg, rgba(240, 98, 146, 0.1), rgba(186, 104, 200, 0.1))'
                    : 'linear-gradient(135deg, rgba(100, 181, 246, 0.1), rgba(240, 98, 146, 0.1))'
                }}
                animate={{
                  y: [0, -20, 0],
                  x: [0, 10, 0],
                  scale: [1, 1.1, 1],
                }}
                transition={{
                  duration: 6 + i * 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: i * 0.5
                }}
              />
            ))}

            {/* Subtle animated gradient background */}
            <motion.div
              className="absolute inset-0"
              animate={{
                background: [
                  'linear-gradient(45deg, rgba(240, 98, 146, 0.02), rgba(186, 104, 200, 0.02))',
                  'linear-gradient(45deg, rgba(186, 104, 200, 0.02), rgba(100, 181, 246, 0.02))',
                  'linear-gradient(45deg, rgba(100, 181, 246, 0.02), rgba(240, 98, 146, 0.02))',
                  'linear-gradient(45deg, rgba(240, 98, 146, 0.02), rgba(186, 104, 200, 0.02))'
                ]
              }}
              transition={{
                duration: 12,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Enhanced Section Header */}
            <motion.div
              initial="hidden"
              animate={featuresInView ? "visible" : "hidden"}
              variants={staggerContainer}
              className="text-center mb-20"
            >
              <motion.div variants={staggerItem}>
                {/* Curved divider with brand gradient */}
                <motion.div 
                  className="w-32 h-1 bg-gradient-to-r from-primary via-secondary to-accent mx-auto mb-8 rounded-full"
                  initial={{ width: 0 }}
                  animate={featuresInView ? { width: 128 } : {}}
                  transition={{ duration: 1.2, delay: 0.5 }}
                />

                <motion.h2 
                  className="font-serif text-5xl md:text-6xl lg:text-7xl text-dark mb-6"
                  initial={{ opacity: 0, y: 30 }}
                  animate={featuresInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.8, delay: 0.2 }}
                >
                  Why Choose{' '}
                  <motion.span 
                    className="font-bold bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent relative"
                    initial={{ backgroundPosition: '0%' }}
                    animate={{ 
                      backgroundPosition: ['0%', '100%', '0%'],
                      filter: ['brightness(1)', 'brightness(1.2)', 'brightness(1)']
                    }}
                    transition={{ 
                      duration: 4, 
                      repeat: Infinity, 
                      ease: 'easeInOut' 
                    }}
                  >
                    Pelucas Chic
                    {/* Shimmer effect */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                      initial={{ x: '-100%' }}
                      animate={{ x: ['100%', '200%'] }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        repeatDelay: 2,
                        ease: 'easeInOut'
                      }}
                    />
                  </motion.span>
                </motion.h2>

                <motion.p 
                  className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light"
                  initial={{ opacity: 0, y: 20 }}
                  animate={featuresInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.8, delay: 0.4 }}
                >
                  Experience the difference with our premium quality wigs and exceptional customer service that sets us apart in the industry
                </motion.p>
              </motion.div>
            </motion.div>

            {/* Enhanced Feature Boxes */}
            <motion.div
              initial="hidden"
              animate={featuresInView ? "visible" : "hidden"}
              variants={staggerContainer}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            >
              {[
                {
                  icon: DiamondIcon,
                  title: '100% Human Hair',
                  description: 'Premium quality human hair that looks and feels completely natural with superior longevity',
                  animation: 'shine',
                  gradient: 'from-amber-400 to-yellow-600'
                },
                {
                  icon: ScissorsIcon,
                  title: 'Expert Craftsmanship',
                  description: 'Hand-tied and expertly crafted by skilled artisans using traditional techniques for perfect fit',
                  animation: 'rotate',
                  gradient: 'from-blue-400 to-indigo-600'
                },
                {
                  icon: ReturnIcon,
                  title: '30-Day Guarantee',
                  description: 'Full money-back guarantee if you\'re not completely satisfied with your purchase',
                  animation: 'pulse',
                  gradient: 'from-green-400 to-emerald-600'
                },
                {
                  icon: UsersIcon,
                  title: '10K+ Happy Customers',
                  description: 'Join thousands of satisfied customers worldwide who trust our quality and service',
                  animation: 'expand',
                  gradient: 'from-pink-400 to-rose-600'
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  variants={{
                    hidden: { opacity: 0, y: 60, scale: 0.9 },
                    visible: { 
                      opacity: 1, 
                      y: 0, 
                      scale: 1,
                      transition: { 
                        duration: 0.8, 
                        delay: index * 0.2,
                        ease: [0.25, 0.46, 0.45, 0.94]
                      }
                    }
                  }}
                  className="group relative"
                  whileHover={{ y: -8 }}
                  transition={{ duration: 0.4, ease: 'easeOut' }}
                >
                  {/* Glassmorphism feature container */}
                  <motion.div
                    className="relative p-8 bg-white/60 backdrop-blur-lg rounded-3xl border border-white/30 shadow-lg hover:shadow-2xl transition-all duration-500"
                    whileHover={{
                      boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.15)',
                      backgroundColor: 'rgba(255, 255, 255, 0.8)'
                    }}
                  >
                    {/* Gradient border effect */}
                    <div className="absolute inset-0 rounded-3xl p-[1px] bg-gradient-to-br from-primary/20 via-secondary/10 to-accent/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="w-full h-full bg-white/60 backdrop-blur-lg rounded-[23px]" />
                    </div>

                    {/* Icon with continuous animation */}
                    <motion.div
                      className="relative z-10 mx-auto mb-6 flex justify-center"
                      whileHover={
                        feature.animation === 'shine' ? { scale: 1.1 } :
                        feature.animation === 'rotate' ? { scale: 1.1, rotate: 15 } :
                        feature.animation === 'pulse' ? { scale: 1.1 } :
                        { scale: 1.15 }
                      }
                      transition={{ duration: 0.3 }}
                    >
                      <motion.div
                        animate={
                          feature.animation === 'shine' ? {
                            filter: ['brightness(1)', 'brightness(1.3)', 'brightness(1)'],
                          } :
                          feature.animation === 'rotate' ? {
                            rotate: [0, 5, -5, 0],
                          } :
                          feature.animation === 'pulse' ? {
                            scale: [1, 1.05, 1],
                          } :
                          {
                            scale: [1, 1.02, 1],
                          }
                        }
                        transition={{
                          duration: feature.animation === 'rotate' ? 4 : 3,
                          repeat: Infinity,
                          ease: 'easeInOut'
                        }}
                        className={`p-4 rounded-2xl bg-gradient-to-br ${feature.gradient} shadow-lg`}
                      >
                        <feature.icon className="w-12 h-12 text-white" />
                      </motion.div>
                    </motion.div>

                    {/* Content */}
                    <div className="relative z-10 text-center">
                      <motion.h3 
                        className="text-xl md:text-2xl font-semibold text-dark mb-4 bg-gradient-to-r from-gray-800 to-gray-900 bg-clip-text text-transparent group-hover:from-primary group-hover:to-secondary transition-all duration-300"
                        whileHover={{ scale: 1.02 }}
                      >
                        {feature.title}
                      </motion.h3>
                      <motion.p 
                        className="text-gray-600 leading-relaxed font-light"
                        initial={{ opacity: 0.8 }}
                        whileHover={{ opacity: 1 }}
                      >
                        {feature.description}
                      </motion.p>

                      {/* Decorative underline */}
                      <motion.div
                        className="mt-6 mx-auto h-0.5 bg-gradient-to-r from-primary to-secondary rounded-full"
                        initial={{ width: '30%' }}
                        whileHover={{ width: '80%' }}
                        transition={{ duration: 0.4 }}
                      />
                    </div>
                  </motion.div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </section>
          
          {/* Enhanced Beauty & Wig Care Tips Section */}
        <section className="py-24 bg-gradient-to-br from-white via-slate-50 to-pink-50 relative overflow-hidden">
          {/* Background decorative elements */}
          <div className="absolute inset-0 opacity-30">
            {[...Array(5)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute rounded-full blur-3xl"
                style={{
                  left: `${20 + i * 20}%`,
                  top: `${10 + (i % 2) * 40}%`,
                  width: `${80 + i * 10}px`,
                  height: `${80 + i * 10}px`,
                  background: 'linear-gradient(135deg, rgba(240, 98, 146, 0.1), rgba(186, 104, 200, 0.05))'
                }}
                animate={{
                  y: [0, -15, 0],
                  x: [0, 8, 0],
                  scale: [1, 1.08, 1],
                }}
                transition={{
                  duration: 8 + i * 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: i * 0.8
                }}
              />
            ))}
          </div>

          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Enhanced Section Header */}
            <motion.div
              className="text-center mb-16"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              {/* Decorative curved divider */}
              <motion.div 
                className="w-24 h-1 bg-gradient-to-r from-primary via-secondary to-accent mx-auto mb-8 rounded-full relative"
                initial={{ width: 0 }}
                whileInView={{ width: 96 }}
                transition={{ duration: 1.2, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-primary via-secondary to-accent rounded-full"
                  animate={{
                    background: [
                      'linear-gradient(45deg, #f06292, #ba68c8, #64b5f6)',
                      'linear-gradient(45deg, #ba68c8, #64b5f6, #f06292)',
                      'linear-gradient(45deg, #64b5f6, #f06292, #ba68c8)',
                      'linear-gradient(45deg, #f06292, #ba68c8, #64b5f6)'
                    ]
                  }}
                  transition={{ duration: 4, repeat: Infinity, ease: 'linear' }}
                />
              </motion.div>

              <motion.h2 
                className="font-display text-4xl md:text-5xl lg:text-6xl font-bold text-dark mb-6"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <motion.span 
                  className="bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent"
                  animate={{
                    backgroundPosition: ['0%', '100%', '0%']
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: 'easeInOut'
                  }}
                >
                  Beauty & Wig Care
                </motion.span>{' '}
                <span className="text-gray-800">Tips</span>
              </motion.h2>

              <motion.p 
                className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                viewport={{ once: true }}
              >
                Discover expert tips and techniques to maintain your wigs and enhance your beauty routine with professional advice
              </motion.p>
            </motion.div>

            {/* Blog Categories Filter */}
            <motion.div
              className="flex flex-wrap justify-center gap-4 mb-12"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              viewport={{ once: true }}
            >
              {['All Posts', 'Styling Tips', 'Maintenance', 'Product Care', 'Trending Looks'].map((category, index) => (
                <motion.button
                  key={category}
                  className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                    index === 0 
                      ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-lg' 
                      : 'bg-white/80 text-gray-700 hover:bg-white hover:text-primary border border-gray-200'
                  }`}
                  whileHover={{ 
                    scale: 1.05,
                    boxShadow: index === 0 
                      ? '0 10px 25px -5px rgba(240, 98, 146, 0.4)' 
                      : '0 5px 15px -3px rgba(0, 0, 0, 0.1)'
                  }}
                  whileTap={{ scale: 0.98 }}
                >
                  {category}
                </motion.button>
              ))}
            </motion.div>

            {/* Featured Blog Posts Carousel */}
            <motion.div
              className="relative"
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              viewport={{ once: true }}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
                {[
                  {
                    title: "10 Pro Tips for Natural-Looking Wig Installation",
                    excerpt: "Learn the secrets professionals use to achieve the most natural-looking wig installations that blend seamlessly...",
                    image: "https://images.unsplash.com/photo-1580618672591-eb180b1a973f?w=600&h=400&fit=crop&crop=faces",
                    category: "Styling Tips",
                    readTime: "5 min read",
                    date: "3 days ago",
                    isNew: true,
                    isPopular: false
                  },
                  {
                    title: "Complete Wig Maintenance Guide: Keep Your Investment Looking Fresh",
                    excerpt: "Discover the essential care routine that will extend your wig's lifespan and maintain its beautiful appearance...",
                    image: "https://images.unsplash.com/photo-1487412947147-5cebf100ff38?w=600&h=400&fit=crop&crop=faces",
                    category: "Maintenance",
                    readTime: "8 min read",
                    date: "1 week ago",
                    isNew: false,
                    isPopular: true
                  },
                  {
                    title: "Color Trends 2024: Bold Hues That Turn Heads",
                    excerpt: "Explore this year's hottest wig color trends and find the perfect shade to express your unique style...",
                    image: "https://images.unsplash.com/photo-1560472354-b33ff0c44a5d?w=600&h=400&fit=crop&crop=faces",
                    category: "Trending Looks",
                    readTime: "6 min read",
                    date: "2 weeks ago",
                    isNew: false,
                    isPopular: true
                  }
                ].map((post, index) => (
                  <motion.div
                    key={index}
                    className="group relative bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500"
                    variants={{
                      hidden: { opacity: 0, y: 60, scale: 0.95 },
                      visible: { 
                        opacity: 1, 
                        y: 0, 
                        scale: 1,
                        transition: { 
                          duration: 0.6, 
                          delay: index * 0.2,
                          ease: [0.25, 0.46, 0.45, 0.94]
                        }
                      }
                    }}
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true }}
                    whileHover={{ y: -8, scale: 1.03 }}
                  >
                    {/* Badges */}
                    <div className="absolute top-4 left-4 z-20 flex gap-2">
                      {post.isNew && (
                        <motion.span 
                          className="px-3 py-1 bg-green-500 text-white text-xs font-bold rounded-full flex items-center space-x-1"
                          animate={{ 
                            scale: [1, 1.05, 1],
                            boxShadow: ['0 0 0 0 rgba(34, 197, 94, 0.4)', '0 0 0 10px rgba(34, 197, 94, 0)', '0 0 0 0 rgba(34, 197, 94, 0)']
                          }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          <span className="w-2 h-2 bg-white rounded-full"></span>
                          <span>NEW</span>
                        </motion.span>
                      )}
                      {post.isPopular && (
                        <motion.span 
                          className="px-3 py-1 bg-red-500 text-white text-xs font-bold rounded-full flex items-center space-x-1"
                          animate={{ rotate: [0, 5, -5, 0] }}
                          transition={{ duration: 3, repeat: Infinity }}
                        >
                          <FireIcon className="w-3 h-3" />
                          <span>POPULAR</span>
                        </motion.span>
                      )}
                    </div>

                    {/* Category tag */}
                    <div className="absolute top-4 right-4 z-20">
                      <span className="px-3 py-1 bg-primary/90 text-white text-xs font-medium rounded-full">
                        {post.category}
                      </span>
                    </div>

                    {/* Featured Image */}
                    <div className="relative aspect-[3/2] overflow-hidden">
                      <motion.div
                        className="absolute inset-0"
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.6 }}
                      >
                        <img
                          src={post.image}
                          alt={post.title}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                            const nextElement = e.currentTarget.nextElementSibling as HTMLElement;
                            if (nextElement) {
                              nextElement.style.display = 'flex';
                            }
                          }}
                        />
                        <div 
                          className="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center"
                          style={{ display: 'none' }}
                        >
                          <DiamondIcon className="w-16 h-16 text-white/60" />
                        </div>
                      </motion.div>
                      
                      {/* Glass effect overlay */}
                      <motion.div 
                        className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        initial={{ opacity: 0 }}
                        whileHover={{ opacity: 1 }}
                      />

                      {/* Read more prompt on hover */}
                      <motion.div
                        className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        initial={{ opacity: 0, y: 20 }}
                        whileHover={{ opacity: 1, y: 0 }}
                      >
                        <motion.button
                          className="px-6 py-3 bg-white/90 backdrop-blur-sm text-dark font-semibold rounded-full hover:bg-white transition-colors duration-300 shadow-lg flex items-center space-x-2"
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <span>Read More</span>
                          <ArrowRightIcon className="w-4 h-4" />
                        </motion.button>
                      </motion.div>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                      <motion.h3 
                        className="font-display text-xl md:text-2xl font-bold text-dark mb-3 group-hover:text-primary transition-colors duration-300 line-clamp-2"
                        whileHover={{ scale: 1.02 }}
                      >
                        {post.title}
                      </motion.h3>
                      
                      <motion.p 
                        className="text-gray-600 mb-4 line-clamp-2 leading-relaxed"
                        initial={{ opacity: 0.8 }}
                        whileHover={{ opacity: 1 }}
                      >
                        {post.excerpt}
                      </motion.p>

                      {/* Meta info */}
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <div className="flex items-center space-x-4">
                          <span className="flex items-center space-x-1">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                            </svg>
                            <span>{post.readTime}</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                            </svg>
                            <span>{post.date}</span>
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Decorative bottom border */}
                    <motion.div
                      className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-primary to-secondary"
                      initial={{ width: '0%' }}
                      whileHover={{ width: '100%' }}
                      transition={{ duration: 0.4 }}
                    />
                  </motion.div>
                ))}
              </div>

              {/* Loading state placeholder */}
              <motion.div
                className="text-center py-8"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.5 }}
              >
                <div className="hidden" id="loading-state">
                  <motion.div
                    className="inline-flex items-center space-x-3 text-gray-500"
                    animate={{ opacity: [0.5, 1, 0.5] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <motion.div
                      className="w-2 h-2 bg-primary rounded-full"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity, delay: 0 }}
                    />
                    <motion.div
                      className="w-2 h-2 bg-secondary rounded-full"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
                    />
                    <motion.div
                      className="w-2 h-2 bg-accent rounded-full"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
                    />
                    <span className="ml-3">Loading latest tips...</span>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>

            {/* Enhanced CTA Button */}
            <motion.div 
              className="text-center mt-16"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.8 }}
              viewport={{ once: true }}
            >
              <motion.button
                className="group relative inline-flex items-center space-x-3 px-10 py-5 bg-gradient-to-r from-primary via-secondary to-accent text-white font-bold text-lg rounded-full overflow-hidden shadow-xl"
                whileHover={{ 
                  scale: 1.05,
                  boxShadow: '0 20px 40px -12px rgba(240, 98, 146, 0.4)'
                }}
                whileTap={{ scale: 0.98 }}
                animate={{
                  background: [
                    'linear-gradient(45deg, #f06292, #ba68c8, #64b5f6)',
                    'linear-gradient(45deg, #ba68c8, #64b5f6, #f06292)',
                    'linear-gradient(45deg, #64b5f6, #f06292, #ba68c8)',
                    'linear-gradient(45deg, #f06292, #ba68c8, #64b5f6)'
                  ]
                }}
                transition={{ 
                  duration: 4,
                  repeat: Infinity,
                  ease: 'linear'
                }}
              >
                {/* Shimmer effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                  initial={{ x: '-100%' }}
                  whileHover={{ x: '100%' }}
                  transition={{ duration: 0.6 }}
                />
                
                <span className="relative z-10">Visit Our Blog</span>
                <motion.div 
                  className="relative z-10"
                  whileHover={{ x: 5 }}
                  transition={{ duration: 0.3 }}
                >
                  <ArrowRightIcon className="w-6 h-6" />
                </motion.div>
              </motion.button>
            </motion.div>
          </div>
        </section>

          {/* Instagram Feed Section */}
        <section className="py-20 bg-white">
          <motion.div 
            className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <motion.div 
              className="text-center mb-12"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h2 className="font-display text-4xl md:text-5xl font-bold text-dark mb-4">
                Follow Us on <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">Instagram</span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                See our wigs in action and get inspired by our satisfied customers' styles and transformations.
              </p>
            </motion.div>
              
              <InstagramFeed 
                username="pelucas_chic_human_hair"
                initialPosts={6}
                columns={{ sm: 1, md: 2, lg: 3 }}
                showFilter={false}
                showUsername={true}
                showFollowButton={true}
              />
          </motion.div>
          </section>
        </div>
      </div>
    </div>
  );
}