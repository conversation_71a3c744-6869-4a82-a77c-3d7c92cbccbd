'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Background */}
        <div className="absolute inset-0 z-0">
          <div className="w-full h-full bg-gradient-to-r from-pink-900/80 via-purple-900/60 to-blue-900/80"></div>
        </div>

        {/* Hero Content */}
        <div className="relative z-10 text-center text-white px-4 max-w-6xl mx-auto">
          <div className="space-y-8">
            {/* Logo */}
            <div className="flex justify-center mb-8">
              <Image
                src="/pelucaschiclogo.png"
                alt="Pelucas Chic Logo"
                width={120}
                height={120}
                className="rounded-full shadow-2xl"
              />
            </div>

            <h1 className="text-5xl md:text-7xl font-bold leading-tight">
              Premium Custom
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-pink-400 via-purple-500 to-blue-500">
                Wigs
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto">
              Discover our collection of high-quality human hair wigs crafted with care for a natural, beautiful look.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link 
                href="/catalog"
                className="bg-gradient-to-r from-pink-500 via-purple-600 to-blue-600 text-white px-10 py-4 rounded-full text-lg font-semibold hover:from-pink-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-2xl"
              >
                Shop Now
              </Link>
              
              <Link 
                href="/about"
                className="border-2 border-white text-white px-10 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-gray-900 transition-all duration-300"
              >
                Learn More
              </Link>
            </div>

            {/* Stats */}
            <div className="flex flex-wrap justify-center gap-8 mt-16">
              {[
                { number: '10K+', label: 'Happy Customers' },
                { number: '500+', label: 'Premium Wigs' },
                { number: '4.9★', label: 'Average Rating' },
                { number: '24/7', label: 'Support' }
              ].map((stat, index) => (
                <div
                  key={index}
                  className="text-center"
                >
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">{stat.number}</div>
                  <div className="text-gray-300 text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Why Choose Pelucas Chic?</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We're committed to providing the highest quality wigs with exceptional service
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                title: 'Premium Quality',
                description: '100% human hair wigs with superior craftsmanship',
                icon: '✨'
              },
              {
                title: 'Custom Fit',
                description: 'Personalized sizing and styling for perfect comfort',
                icon: '🛡️'
              },
              {
                title: 'Expert Support',
                description: 'Professional guidance and customer care',
                icon: '❤️'
              },
              {
                title: 'Fast Shipping',
                description: 'Quick and secure delivery worldwide',
                icon: '🚚'
              }
            ].map((feature, index) => (
              <div
                key={index}
                className="text-center p-6 bg-white rounded-xl shadow-sm hover:shadow-lg transition-shadow duration-300"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Products Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Featured Products</h2>
            <p className="text-xl text-gray-600">Discover our most popular wigs</p>
          </div>

          <div className="text-center py-12">
            <p className="text-gray-500 text-lg mb-6">Products will load from your backend when it's running.</p>
            <Link 
              href="/catalog" 
              className="inline-block bg-gradient-to-r from-pink-500 to-purple-600 text-white px-8 py-3 rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200 shadow-lg"
            >
              Browse All Products
            </Link>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Shop by Category</h2>
            <p className="text-xl text-gray-600">Find the perfect wig for your style</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              { name: 'Lace Front Wigs', description: 'Natural hairline with premium lace', emoji: '👩‍🦱' },
              { name: 'Full Lace Wigs', description: 'Complete versatility and styling', emoji: '💁‍♀️' },
              { name: 'Synthetic Wigs', description: 'Affordable and low maintenance', emoji: '🌟' },
              { name: 'Human Hair Wigs', description: '100% premium human hair', emoji: '✨' }
            ].map((category, index) => (
              <Link 
                key={index}
                href={`/catalog?category=${category.name.toLowerCase().replace(/\s+/g, '-')}`}
                className="block bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-300 overflow-hidden group"
              >
                <div className="p-6 text-center">
                  <div className="text-4xl mb-4">{category.emoji}</div>
                  <h3 className="font-semibold text-gray-900 mb-2">{category.name}</h3>
                  <p className="text-gray-600 text-sm">{category.description}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-pink-500 via-purple-600 to-blue-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <div className="text-white">
            <h2 className="text-4xl font-bold mb-4">Ready to Transform Your Look?</h2>
            <p className="text-xl mb-8 opacity-90">
              Join thousands of satisfied customers who trust Pelucas Chic for their hair needs
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/catalog"
                className="bg-white text-purple-600 px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-colors duration-200"
              >
                Shop Now
              </Link>
              
              <Link 
                href="/contact"
                className="border-2 border-white text-white px-8 py-4 rounded-full font-semibold hover:bg-white hover:text-purple-600 transition-all duration-200"
              >
                Get Consultation
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Language/Currency Switchers - Simple Version */}
      <div className="fixed bottom-4 right-4 z-50 flex flex-col space-y-2">
        <div className="bg-white/90 backdrop-blur-sm shadow-lg rounded-lg p-3">
          <div className="text-sm font-medium text-gray-700 mb-2">Language</div>
          <div className="flex space-x-2">
            <button className="px-3 py-1 bg-primary text-white rounded text-sm">EN</button>
            <button className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300">ES</button>
          </div>
        </div>
        
        <div className="bg-white/90 backdrop-blur-sm shadow-lg rounded-lg p-3">
          <div className="text-sm font-medium text-gray-700 mb-2">Currency</div>
          <div className="flex space-x-2">
            <button className="px-3 py-1 bg-primary text-white rounded text-sm">USD</button>
            <button className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300">EUR</button>
          </div>
        </div>
      </div>
    </div>
  );
}
