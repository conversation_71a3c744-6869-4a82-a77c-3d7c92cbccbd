'use client';

import { useState, useEffect } from 'react';
import { Product } from '@/types/product';
import ColorSwatch from '../ui/ColorSwatch';
import { FiCheck } from 'react-icons/fi';
import { motion } from 'framer-motion';

interface VariantSelectorProps {
  label: string;
  options: string[];
  selectedValue: string | null;
  onChange: (value: string) => void;
  variantType: 'color' | 'length' | 'style' | 'capSize' | 'capConstruction';
  product: Product;
}

export default function VariantSelector({
  label,
  options,
  selectedValue,
  onChange,
  variantType,
  product
}: VariantSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  
  // Reset preview URL when selected value changes
  useEffect(() => {
    setPreviewUrl(null);
  }, [selectedValue]);
  
  // Get available variants for this option type
  const getAvailableVariants = (option: string) => {
    return product.variants.filter(variant => 
      variant.properties.some(prop => prop.name === label && prop.value === option)
    );
  };
  
  // Check if a specific option is available based on current selections
  const isOptionAvailable = (option: string) => {
    const variants = getAvailableVariants(option);
    return variants.length > 0 && variants.some(v => v.inventory && v.inventory.inStock);
  };
  
  // Get a color value from color name
  const getColorValue = (colorName: string): string => {
    const colorMap: Record<string, string> = {
      'Black': '#000000',
      'Dark Brown': '#3b2314',
      'Medium Brown': '#654321',
      'Light Brown': '#a17e49',
      'Blonde': '#E5C8A8',
      'Platinum Blonde': '#EEE6C3',
      'Red': '#9B2335',
      'Auburn': '#6e2614',
      'Burgundy': '#800020',
      'Copper': '#b87333',
      'Ginger': '#d44500',
      'Gray': '#808080',
      'Silver': '#c0c0c0',
      'White': '#ffffff',
      'Pink': '#ff77a8',
      'Blue': '#5271ff',
      'Purple': '#a742f5',
      'Green': '#36a853',
      'Ombre Black to Brown': 'linear-gradient(to right, #000000, #3b2314)',
      'Ombre Brown to Blonde': 'linear-gradient(to right, #3b2314, #E5C8A8)',
      'Ombre Black to Red': 'linear-gradient(to right, #000000, #9B2335)',
      'Ombre Brown to Red': 'linear-gradient(to right, #3b2314, #9B2335)',
      'Ombre Black to Blue': 'linear-gradient(to right, #000000, #5271ff)',
      'Ombre Black to Purple': 'linear-gradient(to right, #000000, #a742f5)'
    };
    
    return colorMap[colorName] || '#cccccc';
  };
  
  // Get length representation in inches
  const getLengthInInches = (length: string): string => {
    const lengthMap: Record<string, string> = {
      'short': '8-12"',
      'medium': '14-18"',
      'long': '20-24"',
      'extraLong': '26-30"'
    };
    
    return lengthMap[length] || length;
  };
  
  // Handle mouse enter for preview
  const handleMouseEnter = (option: string) => {
    if (variantType === 'color') {
      // Find a variant with this color and get its image
      const variantsWithColor = product.variants.filter(variant => 
        variant.properties.some(prop => prop.name === 'Color' && prop.value === option)
      );
      
      if (variantsWithColor.length > 0) {
        // Find an image that might represent this color
        const colorImage = product.images.find(img => img.altText && img.altText.includes(option));
        if (colorImage) {
          setPreviewUrl(colorImage.url);
        }
      }
    }
  };
  
  // Handle mouse leave for preview
  const handleMouseLeave = () => {
    setPreviewUrl(null);
  };
  
  // Toggle dropdown state
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };
  
  // Render each option based on variant type
  const renderOption = (option: string) => {
    const isSelected = selectedValue === option;
    const isAvailable = isOptionAvailable(option);
    
    if (variantType === 'color') {
      return (
        <div 
          key={option}
          className="relative"
          onMouseEnter={() => handleMouseEnter(option)}
          onMouseLeave={handleMouseLeave}
        >
          <button
            type="button"
            onClick={() => onChange(option)}
            className={`
              relative w-10 h-10 rounded-full overflow-hidden border-2 transition-all duration-200
              ${isSelected 
                ? 'border-primary shadow-lg scale-110' 
                : 'border-gray-300 hover:border-gray-400'}
              ${!isAvailable ? 'opacity-40 cursor-not-allowed' : 'cursor-pointer'}
            `}
            disabled={!isAvailable}
            aria-label={`Select ${option} color`}
            title={`${option}${!isAvailable ? ' (Out of stock)' : ''}`}
          >
            <ColorSwatch color={getColorValue(option)} />
            {isSelected && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/10">
                <FiCheck className="text-white stroke-2" />
              </div>
            )}
          </button>
          {isSelected && (
            <span className="absolute -bottom-6 left-1/2 -translate-x-1/2 text-xs font-medium text-gray-700 whitespace-nowrap">
              {option}
            </span>
          )}
        </div>
      );
    } else if (variantType === 'length') {
      return (
        <button
          key={option}
          type="button"
          onClick={() => onChange(option)}
          className={`
            relative px-4 py-2 border text-sm font-medium rounded-md transition-all duration-200
            ${isSelected 
              ? 'border-primary bg-primary/5 text-primary' 
              : 'border-gray-300 text-gray-700 hover:border-gray-400'}
            ${!isAvailable ? 'opacity-40 cursor-not-allowed' : 'cursor-pointer'}
          `}
          disabled={!isAvailable}
          aria-label={`Select ${option} length`}
          title={`${option}${!isAvailable ? ' (Out of stock)' : ''}`}
        >
          <div className="flex flex-col items-center">
            <span>{option}</span>
            <span className="text-xs text-gray-500">{getLengthInInches(option)}</span>
            <div className="mt-1 relative w-full h-1 bg-gray-200 rounded-full overflow-hidden">
              <div
                className={`absolute left-0 top-0 h-full bg-primary ${
                  option === 'short' ? 'w-1/4' :
                  option === 'medium' ? 'w-2/4' :
                  option === 'long' ? 'w-3/4' : 'w-full'
                }`}
              ></div>
            </div>
          </div>
        </button>
      );
    } else if (variantType === 'style') {
      return (
        <button
          key={option}
          type="button"
          onClick={() => onChange(option)}
          className={`
            relative px-4 py-2 border text-sm font-medium rounded-md transition-all duration-200
            ${isSelected 
              ? 'border-primary bg-primary/5 text-primary' 
              : 'border-gray-300 text-gray-700 hover:border-gray-400'}
            ${!isAvailable ? 'opacity-40 cursor-not-allowed' : 'cursor-pointer'}
          `}
          disabled={!isAvailable}
          aria-label={`Select ${option} style`}
          title={`${option}${!isAvailable ? ' (Out of stock)' : ''}`}
        >
          <div className="flex items-center gap-2">
            <span className="capitalize">{option}</span>
            {/* Show style icon representation */}
            <span className="text-xs relative">
              {option === 'straight' && '━━━'}
              {option === 'wavy' && '〰️'}
              {option === 'curly' && '≋≋≋'}
              {option === 'body-wave' && '∿∿∿'}
            </span>
          </div>
        </button>
      );
    } else {
      // Default rendering for other variant types
      return (
        <button
          key={option}
          type="button"
          onClick={() => onChange(option)}
          className={`
            relative px-4 py-2 border text-sm font-medium rounded-md transition-all duration-200
            ${isSelected 
              ? 'border-primary bg-primary/5 text-primary' 
              : 'border-gray-300 text-gray-700 hover:border-gray-400'}
            ${!isAvailable ? 'opacity-40 cursor-not-allowed' : 'cursor-pointer'}
          `}
          disabled={!isAvailable}
          aria-label={`Select ${option}`}
          title={`${option}${!isAvailable ? ' (Out of stock)' : ''}`}
        >
          {option}
        </button>
      );
    }
  };
  
  if (options.length === 0) {
    return null;
  }
  
  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-2">
        <label className="block text-sm font-medium text-gray-700">
          {label}
        </label>
        {selectedValue && (
          <span className="text-sm text-gray-500">
            Selected: <span className="font-medium">{selectedValue}</span>
          </span>
        )}
      </div>
      
      {/* Preview image for color variants */}
      {previewUrl && variantType === 'color' && (
        <div className="mb-3">
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="relative w-16 h-16 rounded-md overflow-hidden border border-gray-200"
          >
            <img 
              src={previewUrl} 
              alt="Color preview" 
              className="w-full h-full object-cover"
            />
          </motion.div>
        </div>
      )}
      
      {/* Variant options */}
      <div className={`
        flex flex-wrap gap-3
        ${variantType === 'color' ? 'items-center justify-start mb-8' : ''}
      `}>
        {options.map(option => renderOption(option))}
      </div>
    </div>
  );
} 