const Order = require('../models/Order');
const Product = require('../models/Product');
const User = require('../models/User');

// Get dashboard overview analytics
const getDashboardOverview = async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    let dateFilter = {};
    const now = new Date();
    
    switch (timeframe) {
      case '7d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } };
        break;
      case '30d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } };
        break;
      case '90d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) } };
        break;
      case '1y':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000) } };
        break;
    }

    const [
      // Revenue metrics
      totalRevenue,
      previousPeriodRevenue,
      
      // Order metrics
      totalOrders,
      previousPeriodOrders,
      
      // Customer metrics
      totalCustomers,
      newCustomers,
      
      // Product metrics
      totalProducts,
      activeProducts,
      
      // Recent activity
      recentOrders,
      topProducts,
      
      // Conversion metrics
      conversionData
    ] = await Promise.all([
      // Current period revenue
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $group: { _id: null, total: { $sum: '$pricing.total' } } }
      ]),
      
      // Previous period revenue for comparison
      Order.aggregate([
        { $match: { 
          createdAt: { 
            $gte: new Date(now.getTime() - (timeframe === '7d' ? 14 : timeframe === '30d' ? 60 : 180) * 24 * 60 * 60 * 1000),
            $lt: new Date(now.getTime() - (timeframe === '7d' ? 7 : timeframe === '30d' ? 30 : 90) * 24 * 60 * 60 * 1000)
          },
          'payment.status': 'paid'
        }},
        { $group: { _id: null, total: { $sum: '$pricing.total' } } }
      ]),
      
      // Current period orders
      Order.countDocuments(dateFilter),
      
      // Previous period orders
      Order.countDocuments({
        createdAt: { 
          $gte: new Date(now.getTime() - (timeframe === '7d' ? 14 : timeframe === '30d' ? 60 : 180) * 24 * 60 * 60 * 1000),
          $lt: new Date(now.getTime() - (timeframe === '7d' ? 7 : timeframe === '30d' ? 30 : 90) * 24 * 60 * 60 * 1000)
        }
      }),
      
      // Total customers
      User.countDocuments({ role: 'customer' }),
      
      // New customers in period
      User.countDocuments({ role: 'customer', ...dateFilter }),
      
      // Total products
      Product.countDocuments(),
      
      // Active products
      Product.countDocuments({ status: 'active' }),
      
      // Recent orders
      Order.find(dateFilter)
        .sort({ createdAt: -1 })
        .limit(5)
        .populate('customer.userId', 'firstName lastName')
        .select('orderNumber customer pricing.total orderStatus createdAt')
        .lean(),
      
      // Top selling products
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $unwind: '$items' },
        { $group: {
          _id: '$items.productId',
          totalSold: { $sum: '$items.quantity' },
          revenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
          productName: { $first: '$items.productName' }
        }},
        { $sort: { totalSold: -1 } },
        { $limit: 5 }
      ]),
      
      // Conversion data (simplified)
      Order.aggregate([
        { $match: dateFilter },
        { $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          orders: { $sum: 1 },
          revenue: { $sum: '$pricing.total' }
        }},
        { $sort: { _id: 1 } }
      ])
    ]);

    // Calculate percentage changes
    const currentRevenue = totalRevenue[0]?.total || 0;
    const prevRevenue = previousPeriodRevenue[0]?.total || 0;
    const revenueChange = prevRevenue > 0 ? ((currentRevenue - prevRevenue) / prevRevenue * 100) : 0;
    
    const orderChange = previousPeriodOrders > 0 ? ((totalOrders - previousPeriodOrders) / previousPeriodOrders * 100) : 0;
    
    // Calculate average order value
    const averageOrderValue = totalOrders > 0 ? currentRevenue / totalOrders : 0;

    res.json({
      success: true,
      data: {
        overview: {
          revenue: {
            current: currentRevenue,
            change: revenueChange,
            trend: revenueChange >= 0 ? 'up' : 'down'
          },
          orders: {
            current: totalOrders,
            change: orderChange,
            trend: orderChange >= 0 ? 'up' : 'down'
          },
          customers: {
            total: totalCustomers,
            new: newCustomers
          },
          products: {
            total: totalProducts,
            active: activeProducts
          },
          averageOrderValue
        },
        recentOrders,
        topProducts,
        salesTrend: conversionData
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard overview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard overview',
      error: error.message
    });
  }
};

// Get sales analytics
const getSalesAnalytics = async (req, res) => {
  try {
    const { timeframe = '30d', groupBy = 'day' } = req.query;
    
    let dateFilter = {};
    let groupFormat = '%Y-%m-%d';
    const now = new Date();
    
    switch (timeframe) {
      case '7d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } };
        groupFormat = '%Y-%m-%d';
        break;
      case '30d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } };
        groupFormat = groupBy === 'week' ? '%Y-%U' : '%Y-%m-%d';
        break;
      case '90d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) } };
        groupFormat = '%Y-%m';
        break;
      case '1y':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000) } };
        groupFormat = '%Y-%m';
        break;
    }

    const [
      salesTrend,
      salesByCategory,
      salesByPaymentMethod,
      topSellingProducts,
      salesByRegion
    ] = await Promise.all([
      // Sales trend over time
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $group: {
          _id: { $dateToString: { format: groupFormat, date: '$createdAt' } },
          revenue: { $sum: '$pricing.total' },
          orders: { $sum: 1 },
          averageOrderValue: { $avg: '$pricing.total' }
        }},
        { $sort: { _id: 1 } }
      ]),
      
      // Sales by product category
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $unwind: '$items' },
        { $lookup: {
          from: 'products',
          localField: 'items.productId',
          foreignField: '_id',
          as: 'product'
        }},
        { $unwind: '$product' },
        { $group: {
          _id: '$product.category',
          revenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
          quantity: { $sum: '$items.quantity' }
        }},
        { $sort: { revenue: -1 } }
      ]),
      
      // Sales by payment method
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $group: {
          _id: '$payment.method',
          revenue: { $sum: '$pricing.total' },
          count: { $sum: 1 }
        }},
        { $sort: { revenue: -1 } }
      ]),
      
      // Top selling products
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $unwind: '$items' },
        { $group: {
          _id: '$items.productId',
          productName: { $first: '$items.productName' },
          totalQuantity: { $sum: '$items.quantity' },
          totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
          averagePrice: { $avg: '$items.price' }
        }},
        { $sort: { totalRevenue: -1 } },
        { $limit: 10 }
      ]),
      
      // Sales by region (based on shipping address)
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $group: {
          _id: '$shippingAddress.state',
          revenue: { $sum: '$pricing.total' },
          orders: { $sum: 1 }
        }},
        { $sort: { revenue: -1 } },
        { $limit: 10 }
      ])
    ]);

    res.json({
      success: true,
      data: {
        salesTrend,
        salesByCategory,
        salesByPaymentMethod,
        topSellingProducts,
        salesByRegion
      }
    });
  } catch (error) {
    console.error('Error fetching sales analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch sales analytics',
      error: error.message
    });
  }
};

// Get customer analytics
const getCustomerAnalytics = async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    let dateFilter = {};
    const now = new Date();
    
    switch (timeframe) {
      case '7d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } };
        break;
      case '30d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } };
        break;
      case '90d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) } };
        break;
    }

    const [
      customerGrowth,
      customerSegments,
      customerRetention,
      topCustomers,
      customerLifetimeValue
    ] = await Promise.all([
      // Customer growth over time
      User.aggregate([
        { $match: { role: 'customer', ...dateFilter } },
        { $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          newCustomers: { $sum: 1 }
        }},
        { $sort: { _id: 1 } }
      ]),
      
      // Customer segments
      User.aggregate([
        { $match: { role: 'customer' } },
        { $addFields: {
          segment: {
            $cond: {
              if: { $or: [
                { $gte: ['$customerMetrics.totalSpent', 1000] },
                { $gte: ['$customerMetrics.totalOrders', 5] }
              ]},
              then: 'VIP',
              else: {
                $cond: {
                  if: { $and: [
                    { $gte: ['$customerMetrics.totalSpent', 200] },
                    { $gte: ['$customerMetrics.totalOrders', 2] }
                  ]},
                  then: 'Regular',
                  else: 'New'
                }
              }
            }
          }
        }},
        { $group: {
          _id: '$segment',
          count: { $sum: 1 },
          totalRevenue: { $sum: '$customerMetrics.totalSpent' },
          averageOrderValue: { $avg: '$customerMetrics.averageOrderValue' }
        }}
      ]),
      
      // Customer retention (simplified)
      User.aggregate([
        { $match: { role: 'customer' } },
        { $addFields: {
          daysSinceLastOrder: {
            $divide: [
              { $subtract: [new Date(), '$customerMetrics.lastOrderDate'] },
              1000 * 60 * 60 * 24
            ]
          }
        }},
        { $group: {
          _id: {
            $cond: {
              if: { $lte: ['$daysSinceLastOrder', 30] },
              then: 'Active',
              else: {
                $cond: {
                  if: { $lte: ['$daysSinceLastOrder', 90] },
                  then: 'At Risk',
                  else: 'Inactive'
                }
              }
            }
          },
          count: { $sum: 1 }
        }}
      ]),
      
      // Top customers by value
      User.find({ role: 'customer' })
        .sort({ 'customerMetrics.totalSpent': -1 })
        .limit(10)
        .select('firstName lastName email customerMetrics')
        .lean(),
      
      // Customer lifetime value distribution
      User.aggregate([
        { $match: { role: 'customer' } },
        { $bucket: {
          groupBy: '$customerMetrics.totalSpent',
          boundaries: [0, 100, 500, 1000, 5000, 10000],
          default: '10000+',
          output: {
            count: { $sum: 1 },
            averageOrders: { $avg: '$customerMetrics.totalOrders' }
          }
        }}
      ])
    ]);

    res.json({
      success: true,
      data: {
        customerGrowth,
        customerSegments,
        customerRetention,
        topCustomers,
        customerLifetimeValue
      }
    });
  } catch (error) {
    console.error('Error fetching customer analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer analytics',
      error: error.message
    });
  }
};

// Get product performance analytics
const getProductAnalytics = async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    
    let dateFilter = {};
    const now = new Date();
    
    switch (timeframe) {
      case '7d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) } };
        break;
      case '30d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) } };
        break;
      case '90d':
        dateFilter = { createdAt: { $gte: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000) } };
        break;
    }

    const [
      productPerformance,
      categoryPerformance,
      inventoryStatus,
      productViews
    ] = await Promise.all([
      // Product performance
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $unwind: '$items' },
        { $group: {
          _id: '$items.productId',
          productName: { $first: '$items.productName' },
          totalSold: { $sum: '$items.quantity' },
          totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
          averagePrice: { $avg: '$items.price' },
          orderCount: { $sum: 1 }
        }},
        { $sort: { totalRevenue: -1 } },
        { $limit: 20 }
      ]),
      
      // Category performance
      Order.aggregate([
        { $match: { ...dateFilter, 'payment.status': 'paid' } },
        { $unwind: '$items' },
        { $lookup: {
          from: 'products',
          localField: 'items.productId',
          foreignField: '_id',
          as: 'product'
        }},
        { $unwind: '$product' },
        { $group: {
          _id: '$product.category',
          totalSold: { $sum: '$items.quantity' },
          totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
          productCount: { $addToSet: '$items.productId' }
        }},
        { $addFields: {
          uniqueProducts: { $size: '$productCount' }
        }},
        { $sort: { totalRevenue: -1 } }
      ]),
      
      // Inventory status
      Product.aggregate([
        { $match: { status: 'active' } },
        { $group: {
          _id: {
            $cond: {
              if: { $eq: ['$stockQuantity', 0] },
              then: 'Out of Stock',
              else: {
                $cond: {
                  if: { $lte: ['$stockQuantity', 10] },
                  then: 'Low Stock',
                  else: 'In Stock'
                }
              }
            }
          },
          count: { $sum: 1 }
        }}
      ]),
      
      // Product views (if tracking is implemented)
      Product.find({ status: 'active' })
        .sort({ viewCount: -1 })
        .limit(10)
        .select('name viewCount salesCount images')
        .lean()
    ]);

    res.json({
      success: true,
      data: {
        productPerformance,
        categoryPerformance,
        inventoryStatus,
        mostViewedProducts: productViews
      }
    });
  } catch (error) {
    console.error('Error fetching product analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch product analytics',
      error: error.message
    });
  }
};

module.exports = {
  getDashboardOverview,
  getSalesAnalytics,
  getCustomerAnalytics,
  getProductAnalytics
};
