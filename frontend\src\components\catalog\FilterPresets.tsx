'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface FilterPreset {
  id: string;
  name: string;
  description: string;
  icon: string;
  filters: {
    priceRange?: [number, number];
    colors?: string[];
    lengths?: string[];
    styles?: string[];
    density?: string[];
    capConstruction?: string[];
    material?: string[];
  };
}

interface FilterPresetsProps {
  onApplyPreset: (filters: any) => void;
  className?: string;
}

const presets: FilterPreset[] = [
  {
    id: 'budget-friendly',
    name: 'Budget Friendly',
    description: 'Quality wigs under $200',
    icon: '💰',
    filters: {
      priceRange: [0, 200],
      material: ['synthetic', 'heat-friendly-synthetic']
    }
  },
  {
    id: 'premium-human',
    name: 'Premium Human Hair',
    description: 'Luxury human hair wigs',
    icon: '✨',
    filters: {
      priceRange: [300, 1000],
      material: ['human-remy', 'human-virgin']
    }
  },
  {
    id: 'natural-colors',
    name: 'Natural Colors',
    description: 'Classic natural hair colors',
    icon: '🌿',
    filters: {
      colors: ['black', 'darkBrown', 'mediumBrown', 'lightBrown']
    }
  },
  {
    id: 'blonde-collection',
    name: 'Blonde Collection',
    description: 'All shades of blonde',
    icon: '☀️',
    filters: {
      colors: ['blonde', 'platinum']
    }
  },
  {
    id: 'short-styles',
    name: 'Short & Chic',
    description: 'Short and bob styles',
    icon: '💇‍♀️',
    filters: {
      lengths: ['short', 'bob'],
      styles: ['bob', 'pixie', 'short-layered']
    }
  },
  {
    id: 'long-glamour',
    name: 'Long & Glamorous',
    description: 'Long flowing styles',
    icon: '👸',
    filters: {
      lengths: ['long', 'extra-long'],
      styles: ['straight', 'wavy', 'curly']
    }
  },
  {
    id: 'lace-front',
    name: 'Lace Front',
    description: 'Natural hairline wigs',
    icon: '🎭',
    filters: {
      capConstruction: ['lace-front', 'full-lace']
    }
  },
  {
    id: 'beginner-friendly',
    name: 'Beginner Friendly',
    description: 'Easy to wear and style',
    icon: '🌟',
    filters: {
      capConstruction: ['basic-cap', 'monofilament'],
      material: ['synthetic', 'heat-friendly-synthetic'],
      priceRange: [50, 300]
    }
  }
];

const FilterPresets: React.FC<FilterPresetsProps> = ({ onApplyPreset, className = '' }) => {
  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Quick Filters</h3>
        <span className="text-sm text-gray-500">Popular combinations</span>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
        {presets.map((preset, index) => (
          <motion.button
            key={preset.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
            onClick={() => onApplyPreset(preset.filters)}
            className="group relative bg-white border border-gray-200 rounded-lg p-4 hover:border-primary hover:shadow-md transition-all duration-200 text-left"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {/* Icon */}
            <div className="text-2xl mb-2">{preset.icon}</div>
            
            {/* Content */}
            <div>
              <h4 className="font-medium text-gray-900 group-hover:text-primary transition-colors duration-200">
                {preset.name}
              </h4>
              <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                {preset.description}
              </p>
            </div>
            
            {/* Hover effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
          </motion.button>
        ))}
      </div>
      
      {/* Custom filter hint */}
      <div className="text-center pt-4 border-t border-gray-200">
        <p className="text-sm text-gray-500">
          Can't find what you're looking for? Use the detailed filters below to create your perfect search.
        </p>
      </div>
    </div>
  );
};

export default FilterPresets;
