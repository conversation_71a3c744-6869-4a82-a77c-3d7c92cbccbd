'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  CogIcon,
  EyeIcon,
  VideoCameraIcon,
  MagnifyingGlassIcon,
  GlobeAltIcon,
  CameraIcon,
  PlayIcon,
  ShoppingBagIcon,
  HeartIcon,
  StarIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

interface FeatureToggle {
  id: string;
  name: string;
  description: string;
  category: 'gallery' | 'ecommerce' | 'ai' | 'social' | 'analytics';
  enabled: boolean;
  icon: React.ComponentType<any>;
  isPremium?: boolean;
  dependencies?: string[];
}

export default function FeaturesSettingsPage() {
  const [features, setFeatures] = useState<FeatureToggle[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize features
  useEffect(() => {
    const initialFeatures: FeatureToggle[] = [
      // Gallery Features
      {
        id: 'advanced_gallery_enabled',
        name: 'Advanced Product Gallery',
        description: 'Enable advanced image zoom, pan, and enhanced viewing experience',
        category: 'gallery',
        enabled: false,
        icon: MagnifyingGlassIcon
      },
      {
        id: '360_view_enabled',
        name: '360° Product Views',
        description: 'Allow customers to rotate and view products from all angles',
        category: 'gallery',
        enabled: false,
        icon: EyeIcon,
        isPremium: true,
        dependencies: ['advanced_gallery_enabled']
      },
      {
        id: 'video_gallery_enabled',
        name: 'Video Gallery',
        description: 'Support for product videos in gallery with playback controls',
        category: 'gallery',
        enabled: true,
        icon: VideoCameraIcon
      },
      {
        id: 'live_streaming_enabled',
        name: 'Live Streaming',
        description: 'Enable live product demonstrations and streaming capabilities',
        category: 'gallery',
        enabled: false,
        icon: PlayIcon,
        isPremium: true
      },
      {
        id: 'virtual_try_on_enabled',
        name: 'Virtual Try-On',
        description: 'AI-powered virtual try-on for wigs and accessories',
        category: 'gallery',
        enabled: false,
        icon: CameraIcon,
        isPremium: true
      },

      // E-commerce Features
      {
        id: 'wishlist_enabled',
        name: 'Wishlist',
        description: 'Allow customers to save products to their wishlist',
        category: 'ecommerce',
        enabled: true,
        icon: HeartIcon
      },
      {
        id: 'comparison_enabled',
        name: 'Product Comparison',
        description: 'Enable side-by-side product comparison functionality',
        category: 'ecommerce',
        enabled: true,
        icon: ShoppingBagIcon
      },
      {
        id: 'reviews_enabled',
        name: 'Product Reviews',
        description: 'Customer reviews and ratings system',
        category: 'ecommerce',
        enabled: true,
        icon: StarIcon
      },
      {
        id: 'guest_checkout_enabled',
        name: 'Guest Checkout',
        description: 'Allow customers to checkout without creating an account',
        category: 'ecommerce',
        enabled: true,
        icon: ShoppingBagIcon
      },

      // AI Features
      {
        id: 'ai_recommendations_enabled',
        name: 'AI Product Recommendations',
        description: 'Personalized product recommendations using AI',
        category: 'ai',
        enabled: false,
        icon: CogIcon,
        isPremium: true
      },
      {
        id: 'chatbot_enabled',
        name: 'AI Chatbot',
        description: 'Intelligent customer support chatbot',
        category: 'ai',
        enabled: true,
        icon: ChatBubbleLeftRightIcon
      },
      {
        id: 'voice_search_enabled',
        name: 'Voice Search',
        description: 'Voice-powered product search functionality',
        category: 'ai',
        enabled: false,
        icon: CogIcon,
        isPremium: true
      },

      // Social Features
      {
        id: 'instagram_integration_enabled',
        name: 'Instagram Integration',
        description: 'Display Instagram feed and enable social shopping',
        category: 'social',
        enabled: true,
        icon: GlobeAltIcon
      },
      {
        id: 'social_sharing_enabled',
        name: 'Social Sharing',
        description: 'Share products on social media platforms',
        category: 'social',
        enabled: true,
        icon: GlobeAltIcon
      },

      // Analytics Features
      {
        id: 'advanced_analytics_enabled',
        name: 'Advanced Analytics',
        description: 'Detailed customer behavior and sales analytics',
        category: 'analytics',
        enabled: true,
        icon: CogIcon
      }
    ];

    // Load saved settings
    const savedSettings = localStorage.getItem('feature-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        const updatedFeatures = initialFeatures.map(feature => ({
          ...feature,
          enabled: parsed[feature.id] !== undefined ? parsed[feature.id] : feature.enabled
        }));
        setFeatures(updatedFeatures);
      } catch (error) {
        console.error('Error loading feature settings:', error);
        setFeatures(initialFeatures);
      }
    } else {
      setFeatures(initialFeatures);
    }

    setIsLoading(false);
  }, []);

  const toggleFeature = (featureId: string) => {
    setFeatures(prev => {
      const updated = prev.map(feature => {
        if (feature.id === featureId) {
          const newEnabled = !feature.enabled;
          
          // If disabling a feature, also disable dependent features
          if (!newEnabled) {
            return prev.map(f => {
              if (f.dependencies?.includes(featureId)) {
                return { ...f, enabled: false };
              }
              return f.id === featureId ? { ...f, enabled: newEnabled } : f;
            });
          }
          
          return { ...feature, enabled: newEnabled };
        }
        return feature;
      });

      // Flatten the array if we have nested arrays from dependency handling
      return updated.flat();
    });
    
    setHasChanges(true);
  };

  const saveSettings = async () => {
    setIsSaving(true);
    
    try {
      // Create settings object
      const settings = features.reduce((acc, feature) => {
        acc[feature.id] = feature.enabled;
        return acc;
      }, {} as { [key: string]: boolean });

      // Save to localStorage
      localStorage.setItem('feature-settings', JSON.stringify(settings));

      // In a real app, you would also save to the backend
      await fetch('/api/admin/settings/features', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(settings)
      });

      setHasChanges(false);
    } catch (error) {
      console.error('Error saving settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const resetToDefaults = () => {
    setFeatures(prev => prev.map(feature => ({
      ...feature,
      enabled: ['wishlist_enabled', 'comparison_enabled', 'reviews_enabled', 'guest_checkout_enabled', 'chatbot_enabled', 'instagram_integration_enabled', 'social_sharing_enabled', 'advanced_analytics_enabled', 'video_gallery_enabled'].includes(feature.id)
    })));
    setHasChanges(true);
  };

  const categoryNames = {
    gallery: 'Gallery & Media',
    ecommerce: 'E-commerce',
    ai: 'AI & Automation',
    social: 'Social Features',
    analytics: 'Analytics'
  };

  const categoryIcons = {
    gallery: CameraIcon,
    ecommerce: ShoppingBagIcon,
    ai: CogIcon,
    social: GlobeAltIcon,
    analytics: StarIcon
  };

  const groupedFeatures = features.reduce((acc, feature) => {
    if (!acc[feature.category]) {
      acc[feature.category] = [];
    }
    acc[feature.category].push(feature);
    return acc;
  }, {} as { [key: string]: FeatureToggle[] });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">Loading feature settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Feature Settings</h1>
          <p className="text-gray-600 mt-2">
            Configure which features are enabled for your store. Some features require premium subscription.
          </p>
        </div>
        
        <div className="flex space-x-3">
          <button
            onClick={resetToDefaults}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            Reset to Defaults
          </button>
          
          <button
            onClick={saveSettings}
            disabled={!hasChanges || isSaving}
            className={`px-6 py-2 rounded-lg font-medium transition-all duration-200 ${
              hasChanges && !isSaving
                ? 'bg-primary text-white hover:bg-primary-dark'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>

      {/* Feature Categories */}
      <div className="space-y-8">
        {Object.entries(groupedFeatures).map(([category, categoryFeatures]) => {
          const CategoryIcon = categoryIcons[category as keyof typeof categoryIcons];
          
          return (
            <motion.div
              key={category}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden"
            >
              {/* Category Header */}
              <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <CategoryIcon className="w-6 h-6 text-primary" />
                  <h2 className="text-xl font-semibold text-gray-900">
                    {categoryNames[category as keyof typeof categoryNames]}
                  </h2>
                  <span className="bg-primary/10 text-primary px-2 py-1 rounded-full text-sm font-medium">
                    {categoryFeatures.length} features
                  </span>
                </div>
              </div>

              {/* Features List */}
              <div className="divide-y divide-gray-200">
                {categoryFeatures.map((feature) => {
                  const FeatureIcon = feature.icon;
                  const isDisabled = feature.dependencies?.some(dep => 
                    !features.find(f => f.id === dep)?.enabled
                  );

                  return (
                    <div key={feature.id} className="p-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-start space-x-4">
                          <div className={`p-2 rounded-lg ${
                            feature.enabled && !isDisabled
                              ? 'bg-primary/10 text-primary'
                              : 'bg-gray-100 text-gray-400'
                          }`}>
                            <FeatureIcon className="w-5 h-5" />
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h3 className="text-lg font-medium text-gray-900">
                                {feature.name}
                              </h3>
                              {feature.isPremium && (
                                <span className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                                  PREMIUM
                                </span>
                              )}
                            </div>
                            <p className="text-gray-600 mt-1">{feature.description}</p>
                            
                            {feature.dependencies && (
                              <div className="mt-2">
                                <p className="text-sm text-gray-500">
                                  Requires: {feature.dependencies.map(dep => 
                                    features.find(f => f.id === dep)?.name
                                  ).join(', ')}
                                </p>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Toggle Switch */}
                        <div className="flex items-center">
                          <button
                            onClick={() => toggleFeature(feature.id)}
                            disabled={isDisabled}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                              feature.enabled && !isDisabled
                                ? 'bg-primary'
                                : 'bg-gray-200'
                            } ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${
                                feature.enabled && !isDisabled ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Save Reminder */}
      {hasChanges && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="fixed bottom-6 right-6 bg-primary text-white px-6 py-3 rounded-lg shadow-lg"
        >
          <p className="font-medium">You have unsaved changes</p>
          <button
            onClick={saveSettings}
            className="text-sm underline hover:no-underline"
          >
            Save now
          </button>
        </motion.div>
      )}
    </div>
  );
}
