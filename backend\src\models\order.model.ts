import mongoose, { Document, Schema } from 'mongoose';
import { IUser } from './user.model';
import { IProduct } from './product.model';

export interface IOrderItem {
  product: IProduct | Schema.Types.ObjectId;
  name: string;
  image: string;
  price: number;
  quantity: number;
  variant?: string;
  sku?: string;
  attributes?: {
    [key: string]: string;
  };
}

export interface IOrderAddress {
  street: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
  details?: string;
  address?: string;
  recipientName?: string;
  phoneNumber?: string;
}

export interface IStatusHistoryItem {
  status: string;
  note?: string;
  timestamp: Date;
  updatedBy?: string;
}

export interface IOrder extends Document {
  user: IUser | Schema.Types.ObjectId;
  orderItems: IOrderItem[];
  shippingAddress: IOrderAddress;
  paymentMethod: string;
  paymentResultId?: string;
  paymentResult?: {
    id: string;
    status: string;
    updateTime: string;
    email?: string;
    provider?: string;
    error?: string;
  };
  taxPrice: number;
  shippingPrice: number;
  totalPrice: number;
  discountAmount: number;
  isPaid: boolean;
  paidAt?: Date;
  isDelivered: boolean;
  deliveredAt?: Date;
  status: string;
  statusHistory: IStatusHistoryItem[];
  trackingNumber?: string;
  carrier?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  aliExpressOrder?: {
    orderId: string;
    orderStatus: string;
    trackingNumber?: string;
    trackingUrl?: string;
    lastSyncedAt: Date;
    paymentToSupplierStatus?: 'pending' | 'processing' | 'completed' | 'failed';
    paymentToSupplierDate?: Date;
    costOfGoods?: number;
    paymentAttempts?: number;
    lastPaymentAttempt?: Date;
    storeId?: string;
  };
}

const orderItemSchema = new Schema({
  product: {
    type: Schema.Types.ObjectId,
    ref: 'Product',
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  image: {
    type: String,
    required: true,
  },
  price: {
    type: Number,
    required: true,
  },
  quantity: {
    type: Number,
    required: true,
    min: 1,
  },
  variant: {
    type: String,
  },
  sku: {
    type: String,
  },
  attributes: {
    type: Map,
    of: String,
  },
});

const orderAddressSchema = new Schema({
  street: {
    type: String,
    required: true,
  },
  city: {
    type: String,
    required: true,
  },
  state: {
    type: String,
  },
  postalCode: {
    type: String,
    required: true,
  },
  country: {
    type: String,
    required: true,
  },
  details: {
    type: String,
  },
  address: {
    type: String,
  },
  recipientName: {
    type: String,
  },
  phoneNumber: {
    type: String,
  },
});

const statusHistorySchema = new Schema({
  status: {
    type: String,
    required: true,
  },
  note: {
    type: String,
  },
  timestamp: {
    type: Date,
    default: Date.now,
  },
  updatedBy: {
    type: String,
  },
});

const orderSchema = new Schema(
  {
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    orderItems: [orderItemSchema],
    shippingAddress: orderAddressSchema,
    paymentMethod: {
      type: String,
      required: true,
    },
    paymentResultId: {
      type: String,
    },
    paymentResult: {
      id: { type: String },
      status: { type: String },
      updateTime: { type: String },
      email: { type: String },
      provider: { type: String },
      error: { type: String },
    },
    taxPrice: {
      type: Number,
      required: true,
      default: 0.0,
    },
    shippingPrice: {
      type: Number,
      required: true,
      default: 0.0,
    },
    totalPrice: {
      type: Number,
      required: true,
      default: 0.0,
    },
    discountAmount: {
      type: Number,
      default: 0.0,
    },
    isPaid: {
      type: Boolean,
      required: true,
      default: false,
    },
    paidAt: {
      type: Date,
    },
    isDelivered: {
      type: Boolean,
      required: true,
      default: false,
    },
    deliveredAt: {
      type: Date,
    },
    status: {
      type: String,
      enum: [
        'Pending', 
        'Processing', 
        'Paid', 
        'Shipped', 
        'Delivered', 
        'Cancelled', 
        'Refunded',
        'PartiallyRefunded',
        'PaymentFailed',
        'Disputed',
        'OnHold'
      ],
      default: 'Pending',
    },
    statusHistory: [statusHistorySchema],
    trackingNumber: {
      type: String,
    },
    carrier: {
      type: String,
    },
    notes: {
      type: String,
    },
    aliExpressOrder: {
      orderId: { type: String },
      orderStatus: { type: String },
      trackingNumber: { type: String },
      trackingUrl: { type: String },
      lastSyncedAt: { type: Date },
      paymentToSupplierStatus: { 
        type: String, 
        enum: ['pending', 'processing', 'completed', 'failed'],
        default: 'pending'
      },
      paymentToSupplierDate: { type: Date },
      costOfGoods: { type: Number },
      paymentAttempts: { type: Number, default: 0 },
      lastPaymentAttempt: { type: Date },
      storeId: { type: String }
    }
  },
  {
    timestamps: true,
  }
);

// Add index for faster lookups
orderSchema.index({ user: 1, createdAt: -1 });
orderSchema.index({ status: 1 });
orderSchema.index({ isPaid: 1 });
orderSchema.index({ isDelivered: 1 });

export default mongoose.model<IOrder>('Order', orderSchema); 