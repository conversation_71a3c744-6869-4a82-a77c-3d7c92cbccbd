'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import AdminLayout from '@/components/admin/AdminLayout';
import { voiceSettingsService, VoiceSettings, VoiceCapabilities, VoiceUsageStats } from '@/services/voiceSettingsService';

interface ToggleSwitchProps {
  enabled: boolean;
  onChange: (enabled: boolean) => void;
  disabled?: boolean;
  label: string;
  description?: string;
}

const ToggleSwitch: React.FC<ToggleSwitchProps> = ({ 
  enabled, 
  onChange, 
  disabled = false, 
  label, 
  description 
}) => {
  return (
    <div className="flex items-center justify-between py-3">
      <div className="flex-1">
        <h4 className="text-sm font-medium text-gray-900">{label}</h4>
        {description && (
          <p className="text-sm text-gray-500">{description}</p>
        )}
      </div>
      <button
        type="button"
        onClick={() => !disabled && onChange(!enabled)}
        disabled={disabled}
        className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
          enabled ? 'bg-primary' : 'bg-gray-200'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <span
          className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
            enabled ? 'translate-x-5' : 'translate-x-0'
          }`}
        />
      </button>
    </div>
  );
};

export default function VoiceSettingsPage() {
  const [settings, setSettings] = useState<VoiceSettings | null>(null);
  const [capabilities, setCapabilities] = useState<VoiceCapabilities | null>(null);
  const [usageStats, setUsageStats] = useState<VoiceUsageStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'settings' | 'capabilities' | 'usage'>('settings');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const [settingsData, capabilitiesData, usageData] = await Promise.all([
        voiceSettingsService.getSettings(),
        voiceSettingsService.testCapabilities(),
        voiceSettingsService.getUsageStats()
      ]);
      
      setSettings(settingsData);
      setCapabilities(capabilitiesData);
      setUsageStats(usageData);
    } catch (err: any) {
      setError(err.message || 'Failed to load voice settings');
    } finally {
      setIsLoading(false);
    }
  };

  const updateSettings = async (newSettings: Partial<VoiceSettings>) => {
    if (!settings) return;
    
    try {
      setIsSaving(true);
      setError(null);
      
      const updatedSettings = await voiceSettingsService.updateSettings(newSettings);
      setSettings(updatedSettings);
      
      // Refresh capabilities after settings change
      const newCapabilities = await voiceSettingsService.testCapabilities();
      setCapabilities(newCapabilities);
    } catch (err: any) {
      setError(err.message || 'Failed to update settings');
    } finally {
      setIsSaving(false);
    }
  };

  const testCapabilities = async () => {
    try {
      setError(null);
      const newCapabilities = await voiceSettingsService.testCapabilities();
      setCapabilities(newCapabilities);
    } catch (err: any) {
      setError(err.message || 'Failed to test capabilities');
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center min-h-96">
          <div className="text-center">
            <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
            <p className="mt-4 text-lg text-gray-600">Loading voice settings...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!settings || !capabilities || !usageStats) {
    return (
      <AdminLayout>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading voice settings</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const tabs = [
    { id: 'settings', name: 'Settings', icon: '⚙️' },
    { id: 'capabilities', name: 'Capabilities', icon: '🔧' },
    { id: 'usage', name: 'Usage Stats', icon: '📊' }
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Voice Processing Settings</h1>
          <p className="mt-1 text-sm text-gray-500">
            Configure voice features and API integrations for your store
          </p>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${capabilities.speechToText ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <div>
                <p className="text-sm font-medium text-gray-900">Speech to Text</p>
                <p className="text-xs text-gray-500">{capabilities.speechToText ? 'Active' : 'Inactive'}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${capabilities.textToSpeech ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <div>
                <p className="text-sm font-medium text-gray-900">Text to Speech</p>
                <p className="text-xs text-gray-500">{capabilities.textToSpeech ? 'Active' : 'Inactive'}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${capabilities.voiceChat ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <div>
                <p className="text-sm font-medium text-gray-900">Voice Chat</p>
                <p className="text-xs text-gray-500">{capabilities.voiceChat ? 'Active' : 'Inactive'}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${capabilities.whatsappVoice ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <div>
                <p className="text-sm font-medium text-gray-900">WhatsApp Voice</p>
                <p className="text-xs text-gray-500">{capabilities.whatsappVoice ? 'Active' : 'Inactive'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`group inline-flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'settings' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Speech to Text Settings */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Speech to Text</h3>
                <div className="space-y-4">
                  <ToggleSwitch
                    enabled={settings.speechToText.enabled}
                    onChange={(enabled) => updateSettings({
                      speechToText: { ...settings.speechToText, enabled }
                    })}
                    disabled={!settings.speechToText.apiKeyConfigured}
                    label="Enable Speech to Text"
                    description={!settings.speechToText.apiKeyConfigured ? "API key required" : "Convert speech to text"}
                  />
                  
                  {!settings.speechToText.apiKeyConfigured && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <p className="text-sm text-yellow-800">
                        ⚠️ API key not configured. Please add your OpenAI API key in the environment variables.
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Text to Speech Settings */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Text to Speech</h3>
                <div className="space-y-4">
                  <ToggleSwitch
                    enabled={settings.textToSpeech.enabled}
                    onChange={(enabled) => updateSettings({
                      textToSpeech: { ...settings.textToSpeech, enabled }
                    })}
                    disabled={!settings.textToSpeech.apiKeyConfigured}
                    label="Enable Text to Speech"
                    description={!settings.textToSpeech.apiKeyConfigured ? "API key required" : "Convert text to speech"}
                  />
                  
                  {!settings.textToSpeech.apiKeyConfigured && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <p className="text-sm text-yellow-800">
                        ⚠️ API key not configured. Please add your OpenAI API key in the environment variables.
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Voice Chat Settings */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Voice Chat</h3>
                <div className="space-y-4">
                  <ToggleSwitch
                    enabled={settings.voiceChat.enabled}
                    onChange={(enabled) => updateSettings({
                      voiceChat: { ...settings.voiceChat, enabled }
                    })}
                    disabled={!settings.speechToText.apiKeyConfigured || !settings.textToSpeech.apiKeyConfigured}
                    label="Enable Voice Chat"
                    description="Allow customers to chat using voice"
                  />
                  
                  <ToggleSwitch
                    enabled={settings.voiceChat.autoPlay}
                    onChange={(autoPlay) => updateSettings({
                      voiceChat: { ...settings.voiceChat, autoPlay }
                    })}
                    disabled={!settings.voiceChat.enabled}
                    label="Auto-play Responses"
                    description="Automatically play voice responses"
                  />
                  
                  <ToggleSwitch
                    enabled={settings.voiceChat.showTranscript}
                    onChange={(showTranscript) => updateSettings({
                      voiceChat: { ...settings.voiceChat, showTranscript }
                    })}
                    disabled={!settings.voiceChat.enabled}
                    label="Show Transcript"
                    description="Display text transcript of voice conversations"
                  />
                </div>
              </div>

              {/* WhatsApp Voice Settings */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">WhatsApp Voice</h3>
                <div className="space-y-4">
                  <ToggleSwitch
                    enabled={settings.whatsappVoice.enabled}
                    onChange={(enabled) => updateSettings({
                      whatsappVoice: { ...settings.whatsappVoice, enabled }
                    })}
                    disabled={!settings.whatsappVoice.apiKeyConfigured}
                    label="Enable WhatsApp Voice"
                    description="Process voice messages from WhatsApp"
                  />
                  
                  <ToggleSwitch
                    enabled={settings.whatsappVoice.autoTranscribe}
                    onChange={(autoTranscribe) => updateSettings({
                      whatsappVoice: { ...settings.whatsappVoice, autoTranscribe }
                    })}
                    disabled={!settings.whatsappVoice.enabled}
                    label="Auto-transcribe Voice Messages"
                    description="Automatically convert voice messages to text"
                  />
                  
                  {!settings.whatsappVoice.apiKeyConfigured && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <p className="text-sm text-yellow-800">
                        ⚠️ WhatsApp API key not configured. Please add your WhatsApp Business API key.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'capabilities' && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">System Capabilities</h3>
                <button
                  onClick={testCapabilities}
                  className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors duration-200"
                >
                  Test Capabilities
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">Speech to Text</h4>
                      <p className="text-sm text-gray-500">Convert audio to text</p>
                    </div>
                    <div className={`w-4 h-4 rounded-full ${capabilities.speechToText ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">Text to Speech</h4>
                      <p className="text-sm text-gray-500">Convert text to audio</p>
                    </div>
                    <div className={`w-4 h-4 rounded-full ${capabilities.textToSpeech ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">Voice Chat</h4>
                      <p className="text-sm text-gray-500">Interactive voice conversations</p>
                    </div>
                    <div className={`w-4 h-4 rounded-full ${capabilities.voiceChat ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">WhatsApp Voice</h4>
                      <p className="text-sm text-gray-500">Process WhatsApp voice messages</p>
                    </div>
                    <div className={`w-4 h-4 rounded-full ${capabilities.whatsappVoice ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">Real-time Processing</h4>
                      <p className="text-sm text-gray-500">Live voice processing</p>
                    </div>
                    <div className={`w-4 h-4 rounded-full ${capabilities.realTimeProcessing ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'usage' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Usage Statistics</h3>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Requests:</span>
                    <span className="font-medium">{usageStats.totalRequests.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">This Month:</span>
                    <span className="font-medium">{usageStats.requestsThisMonth.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Cost This Month:</span>
                    <span className="font-medium">${usageStats.costThisMonth.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Error Rate:</span>
                    <span className="font-medium">{(usageStats.errorRate * 100).toFixed(1)}%</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Languages</h3>
                <div className="space-y-3">
                  {usageStats.topLanguages.map((lang, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-gray-600">{lang.language}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-primary h-2 rounded-full" 
                            style={{ width: `${lang.percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">{lang.count}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </motion.div>

        {/* Save Status */}
        {isSaving && (
          <div className="fixed bottom-4 right-4 bg-primary text-white px-4 py-2 rounded-lg shadow-lg">
            Saving settings...
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
